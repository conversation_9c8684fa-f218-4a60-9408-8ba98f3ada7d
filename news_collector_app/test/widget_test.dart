// News Collector App Widget Tests
//
// This file contains widget tests for the News Collector app.
// These tests verify that the UI components work correctly.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'package:news_collector_app/main.dart';
import 'package:news_collector_app/providers/theme_provider.dart';
import 'package:news_collector_app/providers/auth_provider.dart';

void main() {
  group('News Collector App Tests', () {
    testWidgets('App loads and shows home screen', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(const NewsCollectorApp());
      await tester.pumpAndSettle();

      // Verify that the home screen loads
      expect(find.text('News Collector'), findsOneWidget);
      expect(find.text('Welcome to News Collector'), findsOneWidget);
    });

    testWidgets('Navigation works between screens', (WidgetTester tester) async {
      await tester.pumpWidget(const NewsCollectorApp());
      await tester.pumpAndSettle();

      // Test navigation to news screen
      await tester.tap(find.text('Latest News'));
      await tester.pumpAndSettle();
      expect(find.text('Latest News'), findsOneWidget);

      // Test navigation back to home
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();
      expect(find.text('Welcome to News Collector'), findsOneWidget);
    });

    testWidgets('Theme toggle works', (WidgetTester tester) async {
      await tester.pumpWidget(const NewsCollectorApp());
      await tester.pumpAndSettle();

      // Find and tap the theme toggle button in the app bar
      final themeButtons = find.byIcon(Icons.dark_mode);
      if (themeButtons.evaluate().isNotEmpty) {
        await tester.tap(themeButtons.first);
        await tester.pumpAndSettle();

        // After toggle, should show light mode icon
        expect(find.byIcon(Icons.light_mode), findsOneWidget);
      } else {
        // If dark mode icon not found, look for light mode icon
        final lightModeButtons = find.byIcon(Icons.light_mode);
        expect(lightModeButtons, findsOneWidget);

        await tester.tap(lightModeButtons.first);
        await tester.pumpAndSettle();

        // After toggle, should show dark mode icon
        expect(find.byIcon(Icons.dark_mode), findsOneWidget);
      }
    });

    testWidgets('Login form validation works', (WidgetTester tester) async {
      await tester.pumpWidget(const NewsCollectorApp());
      await tester.pumpAndSettle();

      // Navigate to profile screen using bottom navigation
      final profileNavItems = find.byIcon(Icons.person);
      // Use the last one which should be in the bottom navigation
      await tester.tap(profileNavItems.last);
      await tester.pumpAndSettle();

      // Try to login without entering credentials
      await tester.tap(find.text('Sign In'));
      await tester.pumpAndSettle();

      // Should show validation errors
      expect(find.text('Please enter your username'), findsOneWidget);
      expect(find.text('Please enter your password'), findsOneWidget);
    });
  });

  group('Provider Tests', () {
    testWidgets('ThemeProvider works correctly', (WidgetTester tester) async {
      final themeProvider = ThemeProvider();

      await tester.pumpWidget(
        ChangeNotifierProvider.value(
          value: themeProvider,
          child: Consumer<ThemeProvider>(
            builder: (context, provider, child) {
              return MaterialApp(
                themeMode: provider.themeMode,
                home: Scaffold(
                  body: Text('Theme: ${provider.themeMode}'),
                ),
              );
            },
          ),
        ),
      );

      // Initial theme should be system
      expect(find.text('Theme: ThemeMode.system'), findsOneWidget);

      // Toggle theme
      themeProvider.toggleTheme();
      await tester.pumpAndSettle();

      // Should change to light or dark
      expect(find.textContaining('Theme: ThemeMode.'), findsOneWidget);
    });

    testWidgets('AuthProvider works correctly', (WidgetTester tester) async {
      final authProvider = AuthProvider();

      await tester.pumpWidget(
        ChangeNotifierProvider.value(
          value: authProvider,
          child: Consumer<AuthProvider>(
            builder: (context, provider, child) {
              return MaterialApp(
                home: Scaffold(
                  body: Text('Authenticated: ${provider.isAuthenticated}'),
                ),
              );
            },
          ),
        ),
      );

      // Initially should not be authenticated
      expect(find.text('Authenticated: false'), findsOneWidget);
    });
  });
}
