// Unit tests for Provider classes
//
// These tests verify the business logic of our providers without UI dependencies.

import 'package:flutter_test/flutter_test.dart';
import 'package:news_collector_app/providers/theme_provider.dart';
import 'package:news_collector_app/providers/auth_provider.dart';
import 'package:flutter/material.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  group('ThemeProvider Tests', () {
    late ThemeProvider themeProvider;

    setUp(() {
      themeProvider = ThemeProvider();
    });

    test('initial theme mode should be system', () {
      expect(themeProvider.themeMode, ThemeMode.system);
      expect(themeProvider.isDarkMode, false);
    });

    test('toggleTheme should switch between light and dark', () {
      // Start with system mode, toggle should go to light
      themeProvider.toggleTheme();
      expect(themeProvider.themeMode, ThemeMode.light);
      expect(themeProvider.isDarkMode, false);

      // Toggle again should go to dark
      themeProvider.toggleTheme();
      expect(themeProvider.themeMode, ThemeMode.dark);
      expect(themeProvider.isDarkMode, true);

      // Toggle again should go back to light
      themeProvider.toggleTheme();
      expect(themeProvider.themeMode, ThemeMode.light);
      expect(themeProvider.isDarkMode, false);
    });

    test('setThemeMode should set specific theme mode', () {
      themeProvider.setThemeMode(ThemeMode.dark);
      expect(themeProvider.themeMode, ThemeMode.dark);
      expect(themeProvider.isDarkMode, true);

      themeProvider.setThemeMode(ThemeMode.light);
      expect(themeProvider.themeMode, ThemeMode.light);
      expect(themeProvider.isDarkMode, false);

      themeProvider.setThemeMode(ThemeMode.system);
      expect(themeProvider.themeMode, ThemeMode.system);
      expect(themeProvider.isDarkMode, false);
    });
  });

  group('AuthProvider Tests', () {
    late AuthProvider authProvider;

    setUp(() {
      authProvider = AuthProvider();
    });

    test('initial state should be unauthenticated', () {
      expect(authProvider.isAuthenticated, false);
      expect(authProvider.token, null);
      expect(authProvider.username, null);
    });

    test('login should authenticate user', () async {
      const testUsername = 'testuser';
      const testPassword = 'testpass';

      await authProvider.login(testUsername, testPassword);

      expect(authProvider.isAuthenticated, true);
      expect(authProvider.username, testUsername);
      expect(authProvider.token, isNotNull);
      expect(authProvider.token, startsWith('mock_jwt_token_'));
    });

    test('logout should clear authentication', () async {
      // First login
      await authProvider.login('testuser', 'testpass');
      expect(authProvider.isAuthenticated, true);

      // Then logout
      await authProvider.logout();
      expect(authProvider.isAuthenticated, false);
      expect(authProvider.token, null);
      expect(authProvider.username, null);
    });

    test('login with empty credentials should throw exception', () async {
      // This test would be more meaningful with actual validation
      // For now, our mock login doesn't validate credentials
      expect(() async => await authProvider.login('', ''), returnsNormally);
    });
  });
}
