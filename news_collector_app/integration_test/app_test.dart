// Integration tests for News Collector App
//
// These tests verify the complete user flows and app behavior.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import 'package:news_collector_app/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('News Collector App Integration Tests', () {
    testWidgets('Complete user flow: Home -> News -> Profile -> Login', (tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Verify home screen loads
      expect(find.text('Welcome to News Collector'), findsOneWidget);
      expect(find.text('Quick Actions'), findsOneWidget);

      // Navigate to news screen
      await tester.tap(find.text('Latest News'));
      await tester.pumpAndSettle();

      // Verify news screen
      expect(find.text('Latest News'), findsOneWidget);
      expect(find.text('All'), findsOneWidget); // Category filter
      
      // Test category filtering
      await tester.tap(find.text('Technology'));
      await tester.pumpAndSettle();
      
      // Test search functionality
      await tester.tap(find.byIcon(Icons.search));
      await tester.pumpAndSettle();
      
      await tester.enterText(find.byType(TextField), 'Flutter');
      await tester.pumpAndSettle();
      
      // Close search
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // Navigate to profile screen
      await tester.tap(find.byIcon(Icons.person).last);
      await tester.pumpAndSettle();

      // Should show login form since not authenticated
      expect(find.text('Welcome to News Collector'), findsOneWidget);
      expect(find.text('Please sign in to continue'), findsOneWidget);

      // Test login form validation
      await tester.tap(find.text('Sign In'));
      await tester.pumpAndSettle();

      expect(find.text('Please enter your username'), findsOneWidget);
      expect(find.text('Please enter your password'), findsOneWidget);

      // Fill in login form
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Username'),
        'testuser',
      );
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Password'),
        'testpass',
      );

      // Submit login
      await tester.tap(find.text('Sign In'));
      await tester.pumpAndSettle();

      // Should now show profile content
      expect(find.text('testuser'), findsOneWidget);
      expect(find.text('News Collector Member'), findsOneWidget);

      // Test theme toggle
      final themeSwitch = find.byType(Switch);
      expect(themeSwitch, findsOneWidget);
      
      await tester.tap(themeSwitch);
      await tester.pumpAndSettle();

      // Test logout
      await tester.tap(find.text('Sign Out'));
      await tester.pumpAndSettle();

      // Confirm logout dialog
      await tester.tap(find.text('Sign Out').last);
      await tester.pumpAndSettle();

      // Should return to login form
      expect(find.text('Please sign in to continue'), findsOneWidget);
    });

    testWidgets('News screen functionality', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to news screen
      await tester.tap(find.text('Latest News'));
      await tester.pumpAndSettle();

      // Test pull-to-refresh
      await tester.fling(find.byType(ListView), const Offset(0, 300), 1000);
      await tester.pumpAndSettle();

      // Test news card interaction
      final newsCards = find.byType(Card);
      expect(newsCards, findsWidgets);

      // Tap on first news card to open detail
      await tester.tap(newsCards.first);
      await tester.pumpAndSettle();

      // Should show bottom sheet with news detail
      expect(find.byType(DraggableScrollableSheet), findsOneWidget);

      // Close the bottom sheet by tapping outside or dragging down
      await tester.tapAt(const Offset(50, 50));
      await tester.pumpAndSettle();
    });

    testWidgets('Navigation between screens works correctly', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Test bottom navigation
      final bottomNavItems = find.byType(BottomNavigationBarItem);
      expect(bottomNavItems, findsNWidgets(3));

      // Navigate to news via bottom nav
      await tester.tap(find.byIcon(Icons.newspaper));
      await tester.pumpAndSettle();
      expect(find.text('Latest News'), findsOneWidget);

      // Navigate to profile via bottom nav
      await tester.tap(find.byIcon(Icons.person).last);
      await tester.pumpAndSettle();
      expect(find.text('Please sign in to continue'), findsOneWidget);

      // Navigate back to home via bottom nav
      await tester.tap(find.byIcon(Icons.home));
      await tester.pumpAndSettle();
      expect(find.text('Welcome to News Collector'), findsOneWidget);
    });

    testWidgets('Theme persistence works', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to profile and login
      await tester.tap(find.byIcon(Icons.person));
      await tester.pumpAndSettle();

      await tester.enterText(
        find.widgetWithText(TextFormField, 'Username'),
        'testuser',
      );
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Password'),
        'testpass',
      );
      await tester.tap(find.text('Sign In'));
      await tester.pumpAndSettle();

      // Toggle theme
      await tester.tap(find.byType(Switch));
      await tester.pumpAndSettle();

      // Navigate away and back
      await tester.tap(find.byIcon(Icons.home));
      await tester.pumpAndSettle();
      
      await tester.tap(find.byIcon(Icons.person).last);
      await tester.pumpAndSettle();

      // Theme setting should be preserved
      final themeSwitch = find.byType(Switch);
      final switchWidget = tester.widget<Switch>(themeSwitch);
      expect(switchWidget.value, true); // Should be dark mode
    });
  });
}
