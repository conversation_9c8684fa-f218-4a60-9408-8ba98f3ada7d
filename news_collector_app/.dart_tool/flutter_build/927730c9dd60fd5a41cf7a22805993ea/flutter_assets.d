 /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-bihnwrpqavutgpavxrazoebsqiwm/Build/Products/Debug-iphoneos/App.framework/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-bihnwrpqavutgpavxrazoebsqiwm/Build/Products/Debug-iphoneos/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-bihnwrpqavutgpavxrazoebsqiwm/Build/Products/Debug-iphoneos/App.framework/flutter_assets/shaders/ink_sparkle.frag /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-bihnwrpqavutgpavxrazoebsqiwm/Build/Products/Debug-iphoneos/App.framework/flutter_assets/AssetManifest.json /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-bihnwrpqavutgpavxrazoebsqiwm/Build/Products/Debug-iphoneos/App.framework/flutter_assets/AssetManifest.bin /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-bihnwrpqavutgpavxrazoebsqiwm/Build/Products/Debug-iphoneos/App.framework/flutter_assets/FontManifest.json /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-bihnwrpqavutgpavxrazoebsqiwm/Build/Products/Debug-iphoneos/App.framework/flutter_assets/NOTICES.Z /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-bihnwrpqavutgpavxrazoebsqiwm/Build/Products/Debug-iphoneos/App.framework/flutter_assets/NativeAssetsManifest.json:  /Users/<USER>/Desktop/VS\ code\ file/News_collector_UI/news_collector_app/pubspec.yaml /Users/<USER>/Desktop/VS\ code\ file/News_collector_UI/news_collector_app/ios/Runner/Info.plist /Users/<USER>/Desktop/VS\ code\ file/News_collector_UI/news_collector_app/ios/Flutter/AppFrameworkInfo.plist /Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf /opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf /opt/homebrew/Caskroom/flutter/3.32.5/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag /Users/<USER>/Desktop/VS\ code\ file/News_collector_UI/news_collector_app/.dart_tool/flutter_build/927730c9dd60fd5a41cf7a22805993ea/native_assets.json /Users/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-85.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/analyzer-7.5.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/build-2.5.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-2.5.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.5.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-9.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/built_value-8.10.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/dart_style-3.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/dio_web_adapter-2.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/go_router-14.8.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/io-1.0.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/json_serializable-6.9.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/package_config-2.2.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/process-5.0.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.2.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.5.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-3.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/source_gen-2.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/source_helper-1.3.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sync_http-0.3.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/webdriver-3.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/LICENSE /opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/pkg/sky_engine/LICENSE /opt/homebrew/Caskroom/flutter/3.32.5/flutter/packages/flutter/LICENSE