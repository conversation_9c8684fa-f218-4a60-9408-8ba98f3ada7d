{"inputs": ["/opt/homebrew/Caskroom/flutter/3.32.5/flutter/packages/flutter_tools/lib/src/build_system/targets/native_assets.dart", "/Users/<USER>/Desktop/VS code file/News_collector_UI/news_collector_app/.dart_tool/package_config_subset"], "outputs": ["/Users/<USER>/Desktop/VS code file/News_collector_UI/news_collector_app/.dart_tool/flutter_build/927730c9dd60fd5a41cf7a22805993ea/dart_build_result.json", "/Users/<USER>/Desktop/VS code file/News_collector_UI/news_collector_app/.dart_tool/flutter_build/927730c9dd60fd5a41cf7a22805993ea/dart_build_result.json"]}