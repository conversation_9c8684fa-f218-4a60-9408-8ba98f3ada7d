{"inputs": ["/opt/homebrew/Caskroom/flutter/3.32.5/flutter/packages/flutter_tools/lib/src/build_system/targets/native_assets.dart", "/Users/<USER>/Desktop/VS code file/News_collector_UI/news_collector_app/.dart_tool/package_config_subset"], "outputs": ["/Users/<USER>/Desktop/VS code file/News_collector_UI/news_collector_app/.dart_tool/flutter_build/cdc8d19849481348242e50fe49ad6f73/dart_build_result.json", "/Users/<USER>/Desktop/VS code file/News_collector_UI/news_collector_app/.dart_tool/flutter_build/cdc8d19849481348242e50fe49ad6f73/dart_build_result.json"]}