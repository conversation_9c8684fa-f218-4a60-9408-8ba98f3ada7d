["/Users/<USER>/Desktop/VS code file/News_collector_UI/news_collector_app/ios/Flutter/ephemeral/flutter_lldbinit", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-bihnwrpqavutgpavxrazoebsqiwm/Build/Products/Debug-iphoneos/Flutter.framework/Flutter", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-bihnwrpqavutgpavxrazoebsqiwm/Build/Products/Debug-iphoneos/App.framework/flutter_assets/vm_snapshot_data", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-bihnwrpqavutgpavxrazoebsqiwm/Build/Products/Debug-iphoneos/App.framework/flutter_assets/isolate_snapshot_data", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-bihnwrpqavutgpavxrazoebsqiwm/Build/Products/Debug-iphoneos/App.framework/flutter_assets/kernel_blob.bin", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-bihnwrpqavutgpavxrazoebsqiwm/Build/Products/Debug-iphoneos/App.framework/App", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-bihnwrpqavutgpavxrazoebsqiwm/Build/Products/Debug-iphoneos/App.framework/Info.plist", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-bihnwrpqavutgpavxrazoebsqiwm/Build/Products/Debug-iphoneos/App.framework/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-bihnwrpqavutgpavxrazoebsqiwm/Build/Products/Debug-iphoneos/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-bihnwrpqavutgpavxrazoebsqiwm/Build/Products/Debug-iphoneos/App.framework/flutter_assets/shaders/ink_sparkle.frag", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-bihnwrpqavutgpavxrazoebsqiwm/Build/Products/Debug-iphoneos/App.framework/flutter_assets/AssetManifest.json", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-bihnwrpqavutgpavxrazoebsqiwm/Build/Products/Debug-iphoneos/App.framework/flutter_assets/AssetManifest.bin", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-bihnwrpqavutgpavxrazoebsqiwm/Build/Products/Debug-iphoneos/App.framework/flutter_assets/FontManifest.json", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-bihnwrpqavutgpavxrazoebsqiwm/Build/Products/Debug-iphoneos/App.framework/flutter_assets/NOTICES.Z", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-bihnwrpqavutgpavxrazoebsqiwm/Build/Products/Debug-iphoneos/App.framework/flutter_assets/NativeAssetsManifest.json"]