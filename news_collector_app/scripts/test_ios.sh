#!/bin/bash

# iOS Testing Script for News Collector App
# This script helps set up and test the app on iOS devices

echo "🍎 News Collector iOS Testing Script"
echo "===================================="

# Check Flutter installation
echo "📱 Checking Flutter installation..."
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter is not installed or not in PATH"
    exit 1
fi

# Run Flutter doctor for iOS
echo "🔍 Running Flutter doctor for iOS..."
flutter doctor --verbose

# Check for connected iOS devices
echo "📱 Checking for connected iOS devices..."
flutter devices

# Clean and get dependencies
echo "🧹 Cleaning project and getting dependencies..."
flutter clean
flutter pub get

# Run tests
echo "🧪 Running tests..."
flutter test

# Check if iOS device is connected
DEVICE_COUNT=$(flutter devices | grep -c "ios")
if [ $DEVICE_COUNT -eq 0 ]; then
    echo "⚠️  No iOS devices found. Please:"
    echo "   1. Connect your iOS device via USB"
    echo "   2. Trust the computer on your device"
    echo "   3. Ensure your device is unlocked"
    echo "   4. Run this script again"
    echo ""
    echo "🖥️  Alternatively, you can:"
    echo "   - Open iOS Simulator: 'open -a Simulator'"
    echo "   - Open Xcode project: 'open ios/Runner.xcworkspace'"
    exit 1
fi

# Build for iOS
echo "🔨 Building for iOS..."
flutter build ios --debug --no-codesign

# Run on iOS device
echo "🚀 Launching app on iOS device..."
flutter run --debug

echo "✅ iOS testing setup complete!"
echo ""
echo "📝 Testing Checklist:"
echo "   □ App launches successfully"
echo "   □ Navigation works between screens"
echo "   □ Theme toggle functions properly"
echo "   □ Login form validation works"
echo "   □ News screen displays mock data"
echo "   □ Search functionality works"
echo "   □ Profile screen shows user data after login"
echo "   □ Bottom navigation works correctly"
echo "   □ App responds to device orientation changes"
echo "   □ No crashes or performance issues"
