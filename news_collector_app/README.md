# news_collector_app

A new Flutter project.

## Getting Started

# News Collector App

Information Intelligence Platform - A modern Flutter app for aggregated news, guides, and opportunities.

## 🚀 Features

- **Modern UI**: Material Design 3 with dark/light theme support
- **News Browsing**: Category filtering, search functionality, and article details
- **User Authentication**: Secure login/logout with encrypted storage
- **Responsive Design**: Optimized for different screen sizes
- **Cross-Platform**: iOS and Android support
- **State Management**: Provider pattern for efficient state handling

## 📱 Screenshots

*Screenshots will be added after iOS device testing*

## 🛠️ Getting Started

### Prerequisites

- Flutter SDK (3.8.1 or higher)
- Dart SDK
- Xcode (for iOS development)
- iOS device or simulator

### Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd news_collector_app
   ```

2. **Install dependencies**:
   ```bash
   flutter pub get
   ```

3. **Run the app**:
   ```bash
   flutter run
   ```

## 🧪 Testing

### Run Tests
```bash
# Run all tests
flutter test

# Run specific test file
flutter test test/widget_test.dart

# Run integration tests
flutter test integration_test/
```

### iOS Testing with Xcode

1. **Open iOS project in Xcode**:
   ```bash
   open ios/Runner.xcworkspace
   ```

2. **Configure signing & capabilities**:
   - Select your development team
   - Ensure bundle identifier is unique

3. **Run iOS testing script**:
   ```bash
   ./scripts/test_ios.sh
   ```

## 🏗️ Architecture

```
lib/
├── main.dart                 # App entry point
├── providers/               # State management
│   ├── auth_provider.dart   # Authentication state
│   └── theme_provider.dart  # Theme state
├── screens/                 # UI screens
│   ├── home_screen.dart     # Home page
│   ├── news_screen.dart     # News browsing
│   └── profile_screen.dart  # User profile
└── test/                    # Test files
    ├── widget_test.dart     # Widget tests
    ├── unit_tests/          # Unit tests
    └── integration_test/    # Integration tests
```

## 📦 Dependencies

- **flutter**: SDK
- **provider**: State management
- **go_router**: Navigation
- **dio**: HTTP client
- **flutter_secure_storage**: Secure data storage
- **shared_preferences**: Local preferences

## 🎯 Current Status

✅ **Completed**:
- Flutter project setup with iOS support
- Core app architecture with Provider state management
- Home, News, and Profile screens
- Authentication flow with secure storage
- Theme switching (dark/light mode)
- Navigation with bottom navigation bar
- Basic testing structure

🚧 **In Progress**:
- iOS device testing and Xcode integration
- API integration with FastAPI backend

📋 **Next Steps**:
- Connect to real backend API
- Add push notifications
- Implement offline capabilities
- Enhance UI with animations
- App Store deployment

## 🧪 Testing Checklist

When testing on iOS device:

- [ ] App launches successfully
- [ ] Navigation works between screens
- [ ] Theme toggle functions properly
- [ ] Login form validation works
- [ ] News screen displays mock data
- [ ] Search functionality works
- [ ] Profile screen shows user data after login
- [ ] Bottom navigation works correctly
- [ ] App responds to device orientation changes
- [ ] No crashes or performance issues

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For support and questions, please open an issue in the repository.
