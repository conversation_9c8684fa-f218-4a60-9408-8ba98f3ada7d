import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class AuthProvider extends ChangeNotifier {
  bool _isAuthenticated = false;
  String? _token;
  String? _username;
  
  static const _storage = FlutterSecureStorage();
  static const _tokenKey = 'auth_token';
  static const _usernameKey = 'username';
  
  bool get isAuthenticated => _isAuthenticated;
  String? get token => _token;
  String? get username => _username;
  
  AuthProvider() {
    _loadAuthState();
  }
  
  Future<void> login(String username, String password) async {
    try {
      // TODO: Implement actual API call to FastAPI backend
      // For now, simulate login
      await Future.delayed(const Duration(seconds: 1));
      
      // Mock successful login
      _token = 'mock_jwt_token_${DateTime.now().millisecondsSinceEpoch}';
      _username = username;
      _isAuthenticated = true;
      
      // Save to secure storage
      await _storage.write(key: _tokenKey, value: _token);
      await _storage.write(key: _usernameKey, value: _username);
      
      notifyListeners();
    } catch (e) {
      throw Exception('Login failed: $e');
    }
  }
  
  Future<void> logout() async {
    _token = null;
    _username = null;
    _isAuthenticated = false;
    
    // Clear secure storage
    await _storage.delete(key: _tokenKey);
    await _storage.delete(key: _usernameKey);
    
    notifyListeners();
  }
  
  Future<void> _loadAuthState() async {
    try {
      _token = await _storage.read(key: _tokenKey);
      _username = await _storage.read(key: _usernameKey);
      _isAuthenticated = _token != null;
      notifyListeners();
    } catch (e) {
      // Handle storage errors
      _isAuthenticated = false;
    }
  }
}
