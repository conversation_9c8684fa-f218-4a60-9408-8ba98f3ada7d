import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class NewsScreen extends StatefulWidget {
  const NewsScreen({super.key});

  @override
  State<NewsScreen> createState() => _NewsScreenState();
}

class _NewsScreenState extends State<NewsScreen> {
  final List<NewsItem> _mockNews = [
    NewsItem(
      title: 'Flutter 3.24 Released with New Features',
      summary: 'Google announces Flutter 3.24 with improved performance and new widgets.',
      category: 'Technology',
      publishedAt: DateTime.now().subtract(const Duration(hours: 2)),
      imageUrl: null,
    ),
    NewsItem(
      title: 'AI Revolution in Mobile Development',
      summary: 'How artificial intelligence is transforming the way we build mobile applications.',
      category: 'AI',
      publishedAt: DateTime.now().subtract(const Duration(hours: 5)),
      imageUrl: null,
    ),
    NewsItem(
      title: 'Remote Work Trends 2024',
      summary: 'Latest insights on remote work patterns and their impact on productivity.',
      category: 'Business',
      publishedAt: DateTime.now().subtract(const Duration(days: 1)),
      imageUrl: null,
    ),
  ];

  String _selectedCategory = 'All';
  final List<String> _categories = ['All', 'Technology', 'AI', 'Business'];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Latest News'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/'),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearch,
          ),
        ],
      ),
      body: Column(
        children: [
          // Category Filter
          Container(
            height: 50,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _categories.length,
              itemBuilder: (context, index) {
                final category = _categories[index];
                final isSelected = category == _selectedCategory;
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(category),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        _selectedCategory = category;
                      });
                    },
                  ),
                );
              },
            ),
          ),
          
          // News List
          Expanded(
            child: RefreshIndicator(
              onRefresh: _refreshNews,
              child: ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: _filteredNews.length,
                itemBuilder: (context, index) {
                  final news = _filteredNews[index];
                  return NewsCard(news: news);
                },
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: 1,
        onTap: (index) {
          switch (index) {
            case 0:
              context.go('/');
              break;
            case 1:
              // Already on news screen
              break;
            case 2:
              context.go('/profile');
              break;
          }
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.newspaper),
            label: 'News',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'Profile',
          ),
        ],
      ),
    );
  }

  List<NewsItem> get _filteredNews {
    if (_selectedCategory == 'All') {
      return _mockNews;
    }
    return _mockNews.where((news) => news.category == _selectedCategory).toList();
  }

  Future<void> _refreshNews() async {
    // TODO: Implement actual API call to refresh news
    await Future.delayed(const Duration(seconds: 1));
    setState(() {
      // Mock refresh - in real app, this would fetch new data
    });
  }

  void _showSearch() {
    showSearch(
      context: context,
      delegate: NewsSearchDelegate(_mockNews),
    );
  }
}

class NewsItem {
  final String title;
  final String summary;
  final String category;
  final DateTime publishedAt;
  final String? imageUrl;

  NewsItem({
    required this.title,
    required this.summary,
    required this.category,
    required this.publishedAt,
    this.imageUrl,
  });
}

class NewsCard extends StatelessWidget {
  final NewsItem news;

  const NewsCard({super.key, required this.news});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _showNewsDetail(context),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Chip(
                    label: Text(
                      news.category,
                      style: const TextStyle(fontSize: 12),
                    ),
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  const Spacer(),
                  Text(
                    _formatTime(news.publishedAt),
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                news.title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                news.summary,
                style: Theme.of(context).textTheme.bodyMedium,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inMinutes}m ago';
    }
  }

  void _showNewsDetail(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.95,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Text(
                news.title,
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Chip(label: Text(news.category)),
                  const SizedBox(width: 8),
                  Text(_formatTime(news.publishedAt)),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  child: Text(
                    '${news.summary}\n\nThis is where the full article content would be displayed. In a real implementation, this would fetch the complete article from the FastAPI backend.',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class NewsSearchDelegate extends SearchDelegate<String> {
  final List<NewsItem> news;

  NewsSearchDelegate(this.news);

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () => query = '',
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () => close(context, ''),
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return _buildSearchResults();
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return _buildSearchResults();
  }

  Widget _buildSearchResults() {
    final results = news
        .where((item) =>
            item.title.toLowerCase().contains(query.toLowerCase()) ||
            item.summary.toLowerCase().contains(query.toLowerCase()))
        .toList();

    return ListView.builder(
      itemCount: results.length,
      itemBuilder: (context, index) {
        return NewsCard(news: results[index]);
      },
    );
  }
}
