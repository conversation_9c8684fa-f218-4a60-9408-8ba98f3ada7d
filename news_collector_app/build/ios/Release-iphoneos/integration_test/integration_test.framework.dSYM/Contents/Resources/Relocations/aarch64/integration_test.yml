---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Desktop/VS code file/News_collector_UI/news_collector_app/build/ios/Release-iphoneos/integration_test/integration_test.framework/integration_test'
relocations:
  - { offsetInCU: 0x34, offset: 0x5768F, size: 0x8, addend: 0x0, symName: _integration_testVersionString, symObjAddr: 0x0, symBinAddr: 0x5568, symSize: 0x0 }
  - { offsetInCU: 0x69, offset: 0x576C4, size: 0x8, addend: 0x0, symName: _integration_testVersionNumber, symObjAddr: 0x30, symBinAddr: 0x5598, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x57701, size: 0x8, addend: 0x0, symName: '-[FLTIntegrationTestRunner init]', symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0x68 }
  - { offsetInCU: 0xED, offset: 0x577C7, size: 0x8, addend: 0x0, symName: '-[FLTIntegrationTestRunner init]', symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0x68 }
  - { offsetInCU: 0x124, offset: 0x577FE, size: 0x8, addend: 0x0, symName: '-[FLTIntegrationTestRunner testIntegrationTestWithResults:]', symObjAddr: 0x68, symBinAddr: 0x4068, symSize: 0x18C }
  - { offsetInCU: 0x187, offset: 0x57861, size: 0x8, addend: 0x0, symName: '___59-[FLTIntegrationTestRunner testIntegrationTestWithResults:]_block_invoke', symObjAddr: 0x1F4, symBinAddr: 0x41F4, symSize: 0x148 }
  - { offsetInCU: 0x2E0, offset: 0x579BA, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s48b, symObjAddr: 0x33C, symBinAddr: 0x433C, symSize: 0x3C }
  - { offsetInCU: 0x309, offset: 0x579E3, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s48s, symObjAddr: 0x378, symBinAddr: 0x4378, symSize: 0x30 }
  - { offsetInCU: 0x328, offset: 0x57A02, size: 0x8, addend: 0x0, symName: '-[FLTIntegrationTestRunner capturedScreenshotsByName]', symObjAddr: 0x3A8, symBinAddr: 0x43A8, symSize: 0x44 }
  - { offsetInCU: 0x35F, offset: 0x57A39, size: 0x8, addend: 0x0, symName: '+[FLTIntegrationTestRunner testCaseNameFromDartTestName:]', symObjAddr: 0x3EC, symBinAddr: 0x43EC, symSize: 0xE4 }
  - { offsetInCU: 0x3D2, offset: 0x57AAC, size: 0x8, addend: 0x0, symName: '-[FLTIntegrationTestRunner integrationTestPlugin]', symObjAddr: 0x4D0, symBinAddr: 0x44D0, symSize: 0xC }
  - { offsetInCU: 0x409, offset: 0x57AE3, size: 0x8, addend: 0x0, symName: '-[FLTIntegrationTestRunner setIntegrationTestPlugin:]', symObjAddr: 0x4DC, symBinAddr: 0x44DC, symSize: 0x8 }
  - { offsetInCU: 0x448, offset: 0x57B22, size: 0x8, addend: 0x0, symName: '-[FLTIntegrationTestRunner .cxx_destruct]', symObjAddr: 0x4E4, symBinAddr: 0x44E4, symSize: 0xC }
  - { offsetInCU: 0x27, offset: 0x57D11, size: 0x8, addend: 0x0, symName: '-[IntegrationTestIosTest testIntegrationTest:]', symObjAddr: 0x0, symBinAddr: 0x44F0, symSize: 0x194 }
  - { offsetInCU: 0x90, offset: 0x57D7A, size: 0x8, addend: 0x0, symName: '-[IntegrationTestIosTest testIntegrationTest:]', symObjAddr: 0x0, symBinAddr: 0x44F0, symSize: 0x194 }
  - { offsetInCU: 0xFF, offset: 0x57DE9, size: 0x8, addend: 0x0, symName: '___46-[IntegrationTestIosTest testIntegrationTest:]_block_invoke', symObjAddr: 0x194, symBinAddr: 0x4684, symSize: 0xA0 }
  - { offsetInCU: 0x1DA, offset: 0x57EC4, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s, symObjAddr: 0x234, symBinAddr: 0x4724, symSize: 0x28 }
  - { offsetInCU: 0x203, offset: 0x57EED, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s, symObjAddr: 0x25C, symBinAddr: 0x474C, symSize: 0x28 }
  - { offsetInCU: 0x27, offset: 0x5801C, size: 0x8, addend: 0x0, symName: '+[IntegrationTestPlugin instance]', symObjAddr: 0x0, symBinAddr: 0x4774, symSize: 0x40 }
  - { offsetInCU: 0x3A, offset: 0x5802F, size: 0x8, addend: 0x0, symName: '+[IntegrationTestPlugin instance]', symObjAddr: 0x0, symBinAddr: 0x4774, symSize: 0x40 }
  - { offsetInCU: 0x64, offset: 0x58059, size: 0x8, addend: 0x0, symName: _instance.onceToken, symObjAddr: 0x5368, symBinAddr: 0x9480, symSize: 0x0 }
  - { offsetInCU: 0x7A, offset: 0x5806F, size: 0x8, addend: 0x0, symName: _instance.sInstance, symObjAddr: 0x5370, symBinAddr: 0x9488, symSize: 0x0 }
  - { offsetInCU: 0x173, offset: 0x58168, size: 0x8, addend: 0x0, symName: '___33+[IntegrationTestPlugin instance]_block_invoke', symObjAddr: 0x40, symBinAddr: 0x47B4, symSize: 0x30 }
  - { offsetInCU: 0x19A, offset: 0x5818F, size: 0x8, addend: 0x0, symName: '-[IntegrationTestPlugin initForRegistration]', symObjAddr: 0x70, symBinAddr: 0x47E4, symSize: 0x4 }
  - { offsetInCU: 0x1CF, offset: 0x581C4, size: 0x8, addend: 0x0, symName: '-[IntegrationTestPlugin init]', symObjAddr: 0x74, symBinAddr: 0x47E8, symSize: 0x60 }
  - { offsetInCU: 0x206, offset: 0x581FB, size: 0x8, addend: 0x0, symName: '+[IntegrationTestPlugin registerWithRegistrar:]', symObjAddr: 0xD4, symBinAddr: 0x4848, symSize: 0xA8 }
  - { offsetInCU: 0x259, offset: 0x5824E, size: 0x8, addend: 0x0, symName: '-[IntegrationTestPlugin handleMethodCall:result:]', symObjAddr: 0x17C, symBinAddr: 0x48F0, symSize: 0x240 }
  - { offsetInCU: 0x345, offset: 0x5833A, size: 0x8, addend: 0x0, symName: '-[IntegrationTestPlugin capturePngScreenshot]', symObjAddr: 0x3BC, symBinAddr: 0x4B30, symSize: 0x130 }
  - { offsetInCU: 0x3B8, offset: 0x583AD, size: 0x8, addend: 0x0, symName: '___45-[IntegrationTestPlugin capturePngScreenshot]_block_invoke', symObjAddr: 0x4EC, symBinAddr: 0x4C60, symSize: 0x108 }
  - { offsetInCU: 0x41E, offset: 0x58413, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s, symObjAddr: 0x5F4, symBinAddr: 0x4D68, symSize: 0x8 }
  - { offsetInCU: 0x445, offset: 0x5843A, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s, symObjAddr: 0x5FC, symBinAddr: 0x4D70, symSize: 0x8 }
  - { offsetInCU: 0x464, offset: 0x58459, size: 0x8, addend: 0x0, symName: '-[IntegrationTestPlugin testResults]', symObjAddr: 0x604, symBinAddr: 0x4D78, symSize: 0x8 }
  - { offsetInCU: 0x49B, offset: 0x58490, size: 0x8, addend: 0x0, symName: '-[IntegrationTestPlugin setTestResults:]', symObjAddr: 0x60C, symBinAddr: 0x4D80, symSize: 0xC }
  - { offsetInCU: 0x4DC, offset: 0x584D1, size: 0x8, addend: 0x0, symName: '-[IntegrationTestPlugin capturedScreenshotsByName]', symObjAddr: 0x618, symBinAddr: 0x4D8C, symSize: 0xC }
  - { offsetInCU: 0x513, offset: 0x58508, size: 0x8, addend: 0x0, symName: '-[IntegrationTestPlugin .cxx_destruct]', symObjAddr: 0x624, symBinAddr: 0x4D98, symSize: 0x30 }
...
