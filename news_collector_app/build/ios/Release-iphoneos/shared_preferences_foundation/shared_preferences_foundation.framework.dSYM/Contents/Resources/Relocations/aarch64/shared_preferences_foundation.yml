---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Desktop/VS code file/News_collector_UI/news_collector_app/build/ios/Release-iphoneos/shared_preferences_foundation/shared_preferences_foundation.framework/shared_preferences_foundation'
relocations:
  - { offsetInCU: 0x34, offset: 0x584D7, size: 0x8, addend: 0x0, symName: _shared_preferences_foundationVersionString, symObjAddr: 0x0, symBinAddr: 0xE9F0, symSize: 0x0 }
  - { offsetInCU: 0x69, offset: 0x5850C, size: 0x8, addend: 0x0, symName: _shared_preferences_foundationVersionNumber, symObjAddr: 0x40, symBinAddr: 0xEA30, symSize: 0x0 }
  - { offsetInCU: 0x4F, offset: 0x58571, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation19MessagesPigeonCodecC0A0ACvpZ', symObjAddr: 0x19678, symBinAddr: 0x167F0, symSize: 0x0 }
  - { offsetInCU: 0xA2, offset: 0x585C4, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation11PigeonErrorCMa', symObjAddr: 0x3C, symBinAddr: 0x403C, symSize: 0x20 }
  - { offsetInCU: 0xF1, offset: 0x58613, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation9wrapError33_CCD8851934D588835E18C29B514F4159LLySayypSgGypF', symObjAddr: 0x6C, symBinAddr: 0x406C, symSize: 0x368 }
  - { offsetInCU: 0x29E, offset: 0x587C0, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation10nilOrValue33_CCD8851934D588835E18C29B514F4159LLyxSgypSglFSS_Tg5', symObjAddr: 0x3D4, symBinAddr: 0x43D4, symSize: 0xF8 }
  - { offsetInCU: 0x2CD, offset: 0x587EF, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation10nilOrValue33_CCD8851934D588835E18C29B514F4159LLyxSgypSglFSaySSG_Tg5', symObjAddr: 0x4CC, symBinAddr: 0x44CC, symSize: 0xFC }
  - { offsetInCU: 0x3A0, offset: 0x588C2, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation25MessagesPigeonCodecReader33_CCD8851934D588835E18C29B514F4159LLC9readValue6ofTypeypSgs5UInt8V_tFTo', symObjAddr: 0x72C, symBinAddr: 0x472C, symSize: 0xD0 }
  - { offsetInCU: 0x3D7, offset: 0x588F9, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation25MessagesPigeonCodecReader33_CCD8851934D588835E18C29B514F4159LLC4dataAD10Foundation4DataV_tcfcTo', symObjAddr: 0x7FC, symBinAddr: 0x47FC, symSize: 0xA0 }
  - { offsetInCU: 0x45F, offset: 0x58981, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation25MessagesPigeonCodecWriter33_CCD8851934D588835E18C29B514F4159LLC10writeValueyyypFTo', symObjAddr: 0xA50, symBinAddr: 0x4A50, symSize: 0x68 }
  - { offsetInCU: 0x496, offset: 0x589B8, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation25MessagesPigeonCodecWriter33_CCD8851934D588835E18C29B514F4159LLC4dataADSo13NSMutableDataC_tcfcTo', symObjAddr: 0xAB8, symBinAddr: 0x4AB8, symSize: 0x48 }
  - { offsetInCU: 0x51C, offset: 0x58A3E, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation31MessagesPigeonCodecReaderWriter33_CCD8851934D588835E18C29B514F4159LLC6reader4withSo015FlutterStandardG0C10Foundation4DataV_tFTo', symObjAddr: 0xB40, symBinAddr: 0x4B40, symSize: 0xD0 }
  - { offsetInCU: 0x581, offset: 0x58AA3, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation31MessagesPigeonCodecReaderWriter33_CCD8851934D588835E18C29B514F4159LLC6writer4withSo015FlutterStandardH0CSo13NSMutableDataC_tFTo', symObjAddr: 0xC10, symBinAddr: 0x4C10, symSize: 0x38 }
  - { offsetInCU: 0x5F5, offset: 0x58B17, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation25MessagesPigeonCodecReader33_CCD8851934D588835E18C29B514F4159LLCMa', symObjAddr: 0x8BC, symBinAddr: 0x48BC, symSize: 0x20 }
  - { offsetInCU: 0x609, offset: 0x58B2B, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation25MessagesPigeonCodecWriter33_CCD8851934D588835E18C29B514F4159LLCMa', symObjAddr: 0xB20, symBinAddr: 0x4B20, symSize: 0x20 }
  - { offsetInCU: 0x61D, offset: 0x58B3F, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation31MessagesPigeonCodecReaderWriter33_CCD8851934D588835E18C29B514F4159LLCMa', symObjAddr: 0xC68, symBinAddr: 0x4C68, symSize: 0x20 }
  - { offsetInCU: 0x631, offset: 0x58B53, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation19MessagesPigeonCodecC0A0_WZ', symObjAddr: 0xC88, symBinAddr: 0x4C88, symSize: 0x70 }
  - { offsetInCU: 0x676, offset: 0x58B98, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation19MessagesPigeonCodecCfETo', symObjAddr: 0xD80, symBinAddr: 0x4D80, symSize: 0x4 }
  - { offsetInCU: 0x6A1, offset: 0x58BC3, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation19MessagesPigeonCodecCMa', symObjAddr: 0xD84, symBinAddr: 0x4D84, symSize: 0x20 }
  - { offsetInCU: 0x852, offset: 0x58D74, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation26LegacyUserDefaultsApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryL0_p_AA0defG0_pSgSStFZyypSg_yAKctcfU_', symObjAddr: 0xDA4, symBinAddr: 0x4DA4, symSize: 0x1B8 }
  - { offsetInCU: 0x9DE, offset: 0x58F00, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation26LegacyUserDefaultsApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryL0_p_AA0defG0_pSgSStFZyypSg_yAKctcfU0_', symObjAddr: 0x10FC, symBinAddr: 0x50FC, symSize: 0x218 }
  - { offsetInCU: 0xBD4, offset: 0x590F6, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation26LegacyUserDefaultsApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryL0_p_AA0defG0_pSgSStFZyypSg_yAKctcfU1_', symObjAddr: 0x1314, symBinAddr: 0x5314, symSize: 0x220 }
  - { offsetInCU: 0xDD7, offset: 0x592F9, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation26LegacyUserDefaultsApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryL0_p_AA0defG0_pSgSStFZyypSg_yAKctcfU2_', symObjAddr: 0x1534, symBinAddr: 0x5534, symSize: 0x224 }
  - { offsetInCU: 0xFD1, offset: 0x594F3, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation26LegacyUserDefaultsApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryL0_p_AA0defG0_pSgSStFZyypSg_yAKctcfU3_', symObjAddr: 0x1758, symBinAddr: 0x5758, symSize: 0x1DC }
  - { offsetInCU: 0x11E9, offset: 0x5970B, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation26LegacyUserDefaultsApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryL0_p_AA0defG0_pSgSStFZyypSg_yAKctcfU4_', symObjAddr: 0x1934, symBinAddr: 0x5934, symSize: 0x1C0 }
  - { offsetInCU: 0x13F9, offset: 0x5991B, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation26LegacyUserDefaultsApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryL0_p_AA0defG0_pSgSStFZTf4nnnd_n', symObjAddr: 0x2BA8, symBinAddr: 0x6BA8, symSize: 0xB58 }
  - { offsetInCU: 0x1B3B, offset: 0x5A05D, size: 0x8, addend: 0x0, symName: '_$sypSgAAIegn_Iegng_yXlSgABIeyBy_IeyByy_TR', symObjAddr: 0xF5C, symBinAddr: 0x4F5C, symSize: 0xC8 }
  - { offsetInCU: 0x1B53, offset: 0x5A075, size: 0x8, addend: 0x0, symName: '_$syXlSgIeyBy_ypSgIegn_TR', symObjAddr: 0x1024, symBinAddr: 0x5024, symSize: 0xD8 }
  - { offsetInCU: 0x1B6B, offset: 0x5A08D, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation26LegacyUserDefaultsApiSetupCMa', symObjAddr: 0x1AF4, symBinAddr: 0x5AF4, symSize: 0x20 }
  - { offsetInCU: 0x1CBB, offset: 0x5A1DD, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation20UserDefaultsApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryK0_p_AA0deF0_pSgSStFZyypSg_yAKctcfU_', symObjAddr: 0x1B14, symBinAddr: 0x5B14, symSize: 0x2EC }
  - { offsetInCU: 0x1F5A, offset: 0x5A47C, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation20UserDefaultsApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryK0_p_AA0deF0_pSgSStFZyypSg_yAKctcfU0_', symObjAddr: 0x1E00, symBinAddr: 0x5E00, symSize: 0x228 }
  - { offsetInCU: 0x2159, offset: 0x5A67B, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation20UserDefaultsApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryK0_p_AA0deF0_pSgSStFZyypSg_yAKctcfU1_', symObjAddr: 0x2028, symBinAddr: 0x6028, symSize: 0x268 }
  - { offsetInCU: 0x2381, offset: 0x5A8A3, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation20UserDefaultsApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryK0_p_AA0deF0_pSgSStFZyypSg_yAKctcfU2_', symObjAddr: 0x2290, symBinAddr: 0x6290, symSize: 0x29C }
  - { offsetInCU: 0x2593, offset: 0x5AAB5, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation20UserDefaultsApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryK0_p_AA0deF0_pSgSStFZyypSg_yAKctcfU3_', symObjAddr: 0x252C, symBinAddr: 0x652C, symSize: 0x278 }
  - { offsetInCU: 0x2818, offset: 0x5AD3A, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation20UserDefaultsApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryK0_p_AA0deF0_pSgSStFZTf4nnnd_n', symObjAddr: 0x3700, symBinAddr: 0x7700, symSize: 0x990 }
  - { offsetInCU: 0x2E72, offset: 0x5B394, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation20UserDefaultsApiSetupCMa', symObjAddr: 0x27B4, symBinAddr: 0x67B4, symSize: 0x20 }
  - { offsetInCU: 0x2E86, offset: 0x5B3A8, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOy', symObjAddr: 0x27D4, symBinAddr: 0x67D4, symSize: 0x44 }
  - { offsetInCU: 0x2E9A, offset: 0x5B3BC, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOe', symObjAddr: 0x2818, symBinAddr: 0x6818, symSize: 0x44 }
  - { offsetInCU: 0x2EAE, offset: 0x5B3D0, size: 0x8, addend: 0x0, symName: '_$sypWOc', symObjAddr: 0x285C, symBinAddr: 0x685C, symSize: 0x3C }
  - { offsetInCU: 0x2EC2, offset: 0x5B3E4, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0x2898, symBinAddr: 0x6898, symSize: 0x40 }
  - { offsetInCU: 0x2ED6, offset: 0x5B3F8, size: 0x8, addend: 0x0, symName: '_$sypSgWOb', symObjAddr: 0x28D8, symBinAddr: 0x68D8, symSize: 0x48 }
  - { offsetInCU: 0x2EEA, offset: 0x5B40C, size: 0x8, addend: 0x0, symName: '_$sypWOb', symObjAddr: 0x2920, symBinAddr: 0x6920, symSize: 0x10 }
  - { offsetInCU: 0x2F23, offset: 0x5B445, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCSS_Tgm5', symObjAddr: 0x2930, symBinAddr: 0x6930, symSize: 0x80 }
  - { offsetInCU: 0x3057, offset: 0x5B579, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation15UserDefaultsApi_pWOb', symObjAddr: 0x4090, symBinAddr: 0x8090, symSize: 0x18 }
  - { offsetInCU: 0x306B, offset: 0x5B58D, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation20UserDefaultsApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryK0_p_AA0deF0_pSgSStFZyypSg_yAKctcfU3_TA', symObjAddr: 0x40EC, symBinAddr: 0x80EC, symSize: 0x8 }
  - { offsetInCU: 0x307F, offset: 0x5B5A1, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x40F4, symBinAddr: 0x80F4, symSize: 0x10 }
  - { offsetInCU: 0x3093, offset: 0x5B5B5, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x4104, symBinAddr: 0x8104, symSize: 0x8 }
  - { offsetInCU: 0x30A7, offset: 0x5B5C9, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation20UserDefaultsApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryK0_p_AA0deF0_pSgSStFZyypSg_yAKctcfU2_TA', symObjAddr: 0x410C, symBinAddr: 0x810C, symSize: 0x8 }
  - { offsetInCU: 0x30BB, offset: 0x5B5DD, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation20UserDefaultsApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryK0_p_AA0deF0_pSgSStFZyypSg_yAKctcfU1_TA', symObjAddr: 0x4114, symBinAddr: 0x8114, symSize: 0x8 }
  - { offsetInCU: 0x30CF, offset: 0x5B5F1, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation20UserDefaultsApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryK0_p_AA0deF0_pSgSStFZyypSg_yAKctcfU0_TA', symObjAddr: 0x411C, symBinAddr: 0x811C, symSize: 0x8 }
  - { offsetInCU: 0x30E3, offset: 0x5B605, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation20UserDefaultsApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryK0_p_AA0deF0_pSgSStFZyypSg_yAKctcfU_TA', symObjAddr: 0x4124, symBinAddr: 0x8124, symSize: 0x8 }
  - { offsetInCU: 0x30F7, offset: 0x5B619, size: 0x8, addend: 0x0, symName: ___swift_allocate_boxed_opaque_existential_0, symObjAddr: 0x412C, symBinAddr: 0x812C, symSize: 0x3C }
  - { offsetInCU: 0x310B, offset: 0x5B62D, size: 0x8, addend: 0x0, symName: '_$syXlSgIeyBy_ypSgIegn_TRTA', symObjAddr: 0x41E8, symBinAddr: 0x81E8, symSize: 0x8 }
  - { offsetInCU: 0x319B, offset: 0x5B6BD, size: 0x8, addend: 0x0, symName: '_$sSD8IteratorV8_VariantOyxq___GSHRzr0_lWOe', symObjAddr: 0x42C8, symBinAddr: 0x82C8, symSize: 0x8 }
  - { offsetInCU: 0x31AF, offset: 0x5B6D1, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation26LegacyUserDefaultsApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryL0_p_AA0defG0_pSgSStFZyypSg_yAKctcfU4_TA', symObjAddr: 0x4394, symBinAddr: 0x8394, symSize: 0x8 }
  - { offsetInCU: 0x31C3, offset: 0x5B6E5, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation26LegacyUserDefaultsApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryL0_p_AA0defG0_pSgSStFZyypSg_yAKctcfU3_TA', symObjAddr: 0x439C, symBinAddr: 0x839C, symSize: 0x8 }
  - { offsetInCU: 0x31D7, offset: 0x5B6F9, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation26LegacyUserDefaultsApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryL0_p_AA0defG0_pSgSStFZyypSg_yAKctcfU2_TA', symObjAddr: 0x43A4, symBinAddr: 0x83A4, symSize: 0x8 }
  - { offsetInCU: 0x31EB, offset: 0x5B70D, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation26LegacyUserDefaultsApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryL0_p_AA0defG0_pSgSStFZyypSg_yAKctcfU1_TA', symObjAddr: 0x43AC, symBinAddr: 0x83AC, symSize: 0x8 }
  - { offsetInCU: 0x31FF, offset: 0x5B721, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation26LegacyUserDefaultsApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryL0_p_AA0defG0_pSgSStFZyypSg_yAKctcfU0_TA', symObjAddr: 0x43B4, symBinAddr: 0x83B4, symSize: 0x8 }
  - { offsetInCU: 0x3213, offset: 0x5B735, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation26LegacyUserDefaultsApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryL0_p_AA0defG0_pSgSStFZyypSg_yAKctcfU_TA', symObjAddr: 0x43BC, symBinAddr: 0x83BC, symSize: 0x8 }
  - { offsetInCU: 0x3227, offset: 0x5B749, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation30SharedPreferencesPigeonOptionsVwCP', symObjAddr: 0x43C4, symBinAddr: 0x83C4, symSize: 0x2C }
  - { offsetInCU: 0x323B, offset: 0x5B75D, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation30SharedPreferencesPigeonOptionsVwxx', symObjAddr: 0x43F0, symBinAddr: 0x83F0, symSize: 0x8 }
  - { offsetInCU: 0x324F, offset: 0x5B771, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation30SharedPreferencesPigeonOptionsVwca', symObjAddr: 0x43F8, symBinAddr: 0x83F8, symSize: 0x40 }
  - { offsetInCU: 0x3263, offset: 0x5B785, size: 0x8, addend: 0x0, symName: ___swift_memcpy16_8, symObjAddr: 0x4438, symBinAddr: 0x8438, symSize: 0xC }
  - { offsetInCU: 0x3277, offset: 0x5B799, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation30SharedPreferencesPigeonOptionsVwta', symObjAddr: 0x4444, symBinAddr: 0x8444, symSize: 0x30 }
  - { offsetInCU: 0x328B, offset: 0x5B7AD, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation30SharedPreferencesPigeonOptionsVwet', symObjAddr: 0x4474, symBinAddr: 0x8474, symSize: 0x5C }
  - { offsetInCU: 0x329F, offset: 0x5B7C1, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation30SharedPreferencesPigeonOptionsVwst', symObjAddr: 0x44D0, symBinAddr: 0x84D0, symSize: 0x50 }
  - { offsetInCU: 0x32B3, offset: 0x5B7D5, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation30SharedPreferencesPigeonOptionsVMa', symObjAddr: 0x4520, symBinAddr: 0x8520, symSize: 0x10 }
  - { offsetInCU: 0x32F8, offset: 0x5B81A, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation11PigeonErrorCs0E0AAsADP7_domainSSvgTW', symObjAddr: 0x5C, symBinAddr: 0x405C, symSize: 0x4 }
  - { offsetInCU: 0x3314, offset: 0x5B836, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation11PigeonErrorCs0E0AAsADP5_codeSivgTW', symObjAddr: 0x60, symBinAddr: 0x4060, symSize: 0x4 }
  - { offsetInCU: 0x3330, offset: 0x5B852, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation11PigeonErrorCs0E0AAsADP9_userInfoyXlSgvgTW', symObjAddr: 0x64, symBinAddr: 0x4064, symSize: 0x4 }
  - { offsetInCU: 0x334C, offset: 0x5B86E, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation11PigeonErrorCs0E0AAsADP19_getEmbeddedNSErroryXlSgyFTW', symObjAddr: 0x68, symBinAddr: 0x4068, symSize: 0x4 }
  - { offsetInCU: 0x357A, offset: 0x5BA9C, size: 0x8, addend: 0x0, symName: '_$sSaySayxGqd__c7ElementQyd__RszSTRd__lufCSS_SD4KeysVySSyp_GTgm5Tf4g_n', symObjAddr: 0x41F0, symBinAddr: 0x81F0, symSize: 0xD8 }
  - { offsetInCU: 0x3871, offset: 0x5BD93, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation11PigeonErrorCfD', symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0x3C }
  - { offsetInCU: 0x3929, offset: 0x5BE4B, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation25MessagesPigeonCodecReader33_CCD8851934D588835E18C29B514F4159LLC9readValue6ofTypeypSgs5UInt8V_tF', symObjAddr: 0x5C8, symBinAddr: 0x45C8, symSize: 0x164 }
  - { offsetInCU: 0x39F3, offset: 0x5BF15, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation25MessagesPigeonCodecWriter33_CCD8851934D588835E18C29B514F4159LLC10writeValueyyypF', symObjAddr: 0x8DC, symBinAddr: 0x48DC, symSize: 0x174 }
  - { offsetInCU: 0x3C77, offset: 0x5C199, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation20UserDefaultsApiSetupCfD', symObjAddr: 0x27A4, symBinAddr: 0x67A4, symSize: 0x10 }
  - { offsetInCU: 0x3CE2, offset: 0x5C204, size: 0x8, addend: 0x0, symName: '_$sSTsE21_copySequenceContents12initializing8IteratorQz_SitSry7ElementQzG_tFSD4KeysVySSyp_G_Tg5', symObjAddr: 0x29B0, symBinAddr: 0x69B0, symSize: 0x1F8 }
  - { offsetInCU: 0xD8, offset: 0x5C4CD, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation29LegacySharedPreferencesPluginC8register4withySo07FlutterG9Registrar_p_tFZTo', symObjAddr: 0xD4, symBinAddr: 0x8684, symSize: 0xCC }
  - { offsetInCU: 0x158, offset: 0x5C54D, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation29LegacySharedPreferencesPluginC11getAllPrefs6prefix9allowListSDySSypGSS_SaySSGSgtFTf4nnd_n', symObjAddr: 0x2AA0, symBinAddr: 0xB010, symSize: 0x41C }
  - { offsetInCU: 0x376, offset: 0x5C76B, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation29LegacySharedPreferencesPluginC5clear6prefix9allowListSbSS_SaySSGSgtFTf4nnd_n', symObjAddr: 0x2EBC, symBinAddr: 0xB42C, symSize: 0x26C }
  - { offsetInCU: 0x49C, offset: 0x5C891, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation29LegacySharedPreferencesPluginCMa', symObjAddr: 0xB4, symBinAddr: 0x8664, symSize: 0x20 }
  - { offsetInCU: 0x54A, offset: 0x5C93F, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation23SharedPreferencesPluginC8register4withySo07FlutterF9Registrar_p_tFZTo', symObjAddr: 0x42C, symBinAddr: 0x89DC, symSize: 0x30 }
  - { offsetInCU: 0x5BE, offset: 0x5C9B3, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation23SharedPreferencesPluginC15getUserDefaults33_AEA60E88226208FD23718A35E75268ADLL7optionsSo06NSUserI0CAA0dE13PigeonOptionsV_tKFZTf4nd_n', symObjAddr: 0x22E4, symBinAddr: 0xA854, symSize: 0x408 }
  - { offsetInCU: 0x7B8, offset: 0x5CBAD, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation23SharedPreferencesPluginC5clear9allowList7optionsySaySSGSg_AA0dE13PigeonOptionsVtKFTf4nnd_n', symObjAddr: 0x3128, symBinAddr: 0xB698, symSize: 0x27C }
  - { offsetInCU: 0x9CF, offset: 0x5CDC4, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation23SharedPreferencesPluginC8register4withySo07FlutterF9Registrar_p_tFZTf4nd_n', symObjAddr: 0x33A4, symBinAddr: 0xB914, symSize: 0x13C }
  - { offsetInCU: 0xDD4, offset: 0x5D1C9, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV6filteryAByxq_GSbx3key_q_5valuet_tKXEKFSS_ypTg5158$s29shared_preferences_foundation23SharedPreferencesPluginC11getAllPrefs9allowList7optionsSDySSypGSaySSGSg_AA0dE13PigeonOptionsVtKFZSbSS3key_yp5valuet_tXEfU1_07shared_G11_foundation0ijK0CXMTTf1cn_n', symObjAddr: 0x1118, symBinAddr: 0x96C8, symSize: 0x16C }
  - { offsetInCU: 0xF01, offset: 0x5D2F6, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV6filteryAByxq_GSbx3key_q_5valuet_tKXEKFADs13_UnsafeBitsetVKXEfU_SS_ypTg5158$s29shared_preferences_foundation23SharedPreferencesPluginC11getAllPrefs9allowList7optionsSDySSypGSaySSGSg_AA0dE13PigeonOptionsVtKFZSbSS3key_yp5valuet_tXEfU1_07shared_I11_foundation0klM0CXMTTf1nnc_n', symObjAddr: 0x1284, symBinAddr: 0x9834, symSize: 0x1E0 }
  - { offsetInCU: 0x11C5, offset: 0x5D5BA, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV6filteryAByxq_GSbx3key_q_5valuet_tKXEKFADs13_UnsafeBitsetVKXEfU_SS_ypTg5158$s29shared_preferences_foundation23SharedPreferencesPluginC11getAllPrefs9allowList7optionsSDySSypGSaySSGSg_AA0dE13PigeonOptionsVtKFZSbSS3key_yp5valuet_tXEfU0_ShySSGTf1nnc_nTf4nng_n', symObjAddr: 0x26EC, symBinAddr: 0xAC5C, symSize: 0x1E8 }
  - { offsetInCU: 0x1236, offset: 0x5D62B, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV6filteryAByxq_GSbx3key_q_5valuet_tKXEKFSS_ypTg5158$s29shared_preferences_foundation23SharedPreferencesPluginC11getAllPrefs9allowList7optionsSDySSypGSaySSGSg_AA0dE13PigeonOptionsVtKFZSbSS3key_yp5valuet_tXEfU0_ShySSGTf1cn_nTf4ng_n', symObjAddr: 0x28D4, symBinAddr: 0xAE44, symSize: 0x1CC }
  - { offsetInCU: 0x1466, offset: 0x5D85B, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation23SharedPreferencesPluginCMa', symObjAddr: 0x34E0, symBinAddr: 0xBA50, symSize: 0x20 }
  - { offsetInCU: 0x147A, offset: 0x5D86F, size: 0x8, addend: 0x0, symName: ___swift_destroy_boxed_opaque_existential_0, symObjAddr: 0x352C, symBinAddr: 0xBA9C, symSize: 0x20 }
  - { offsetInCU: 0x148E, offset: 0x5D883, size: 0x8, addend: 0x0, symName: '_$sSo12FlutterErrorCMa', symObjAddr: 0x3588, symBinAddr: 0xBABC, symSize: 0x3C }
  - { offsetInCU: 0x14A2, offset: 0x5D897, size: 0x8, addend: 0x0, symName: ___swift_project_boxed_opaque_existential_0, symObjAddr: 0x35C4, symBinAddr: 0xBAF8, symSize: 0x24 }
  - { offsetInCU: 0x1657, offset: 0x5DA4C, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ypTgm5Tf4g_n', symObjAddr: 0x2138, symBinAddr: 0xA6A8, symSize: 0x118 }
  - { offsetInCU: 0x1797, offset: 0x5DB8C, size: 0x8, addend: 0x0, symName: '_$sShyShyxGqd__nc7ElementQyd__RszSTRd__lufCSS_SaySSGTgm5Tf4g_n', symObjAddr: 0x2250, symBinAddr: 0xA7C0, symSize: 0x94 }
  - { offsetInCU: 0x1BAD, offset: 0x5DFA2, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation29LegacySharedPreferencesPluginC8register4withySo07FlutterG9Registrar_p_tFZ', symObjAddr: 0x0, symBinAddr: 0x85B0, symSize: 0xB4 }
  - { offsetInCU: 0x1C86, offset: 0x5E07B, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation23SharedPreferencesPluginC11getAllPrefs9allowList7optionsSDySSypGSaySSGSg_AA0dE13PigeonOptionsVtKFZ', symObjAddr: 0x1A0, symBinAddr: 0x8750, symSize: 0x244 }
  - { offsetInCU: 0x1EBB, offset: 0x5E2B0, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation23SharedPreferencesPluginC8register4withySo07FlutterF9Registrar_p_tFZ', symObjAddr: 0x408, symBinAddr: 0x89B8, symSize: 0x4 }
  - { offsetInCU: 0x1ECF, offset: 0x5E2C4, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation23SharedPreferencesPluginCACycfC', symObjAddr: 0x40C, symBinAddr: 0x89BC, symSize: 0x20 }
  - { offsetInCU: 0x1F11, offset: 0x5E306, size: 0x8, addend: 0x0, symName: '_$sSTsSQ7ElementRpzrlE6starts4withSbqd___tSTRd__AAQyd__ABRSlFSS_SSTg5', symObjAddr: 0x45C, symBinAddr: 0x8A0C, symSize: 0x158 }
  - { offsetInCU: 0x1FDC, offset: 0x5E3D1, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation23SharedPreferencesPluginC8getValue3key7optionsypSgSS_AA0dE13PigeonOptionsVtKF', symObjAddr: 0x5B4, symBinAddr: 0x8B64, symSize: 0x158 }
  - { offsetInCU: 0x2092, offset: 0x5E487, size: 0x8, addend: 0x0, symName: '_$s29shared_preferences_foundation23SharedPreferencesPluginC16isTypeCompatible5valueSbyp_tFZ', symObjAddr: 0x70C, symBinAddr: 0x8CBC, symSize: 0x1A0 }
  - { offsetInCU: 0x2224, offset: 0x5E619, size: 0x8, addend: 0x0, symName: '_$sSh8containsySbxFSS_Tg5', symObjAddr: 0x8AC, symBinAddr: 0x8E5C, symSize: 0x134 }
  - { offsetInCU: 0x22CB, offset: 0x5E6C0, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSS_Tg5', symObjAddr: 0xAA0, symBinAddr: 0x9050, symSize: 0x64 }
  - { offsetInCU: 0x2309, offset: 0x5E6FE, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSS_Tg5', symObjAddr: 0xB04, symBinAddr: 0x90B4, symSize: 0xE0 }
  - { offsetInCU: 0x23A7, offset: 0x5E79C, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_ypTg5', symObjAddr: 0xBE4, symBinAddr: 0x9194, symSize: 0x1F4 }
  - { offsetInCU: 0x2463, offset: 0x5E858, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_ypTg5', symObjAddr: 0xDD8, symBinAddr: 0x9388, symSize: 0x340 }
  - { offsetInCU: 0x256A, offset: 0x5E95F, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV07extractB05using5countAByxq_Gs13_UnsafeBitsetV_SitFSS_ypTg5', symObjAddr: 0x1464, symBinAddr: 0x9A14, symSize: 0x288 }
  - { offsetInCU: 0x2657, offset: 0x5EA4C, size: 0x8, addend: 0x0, symName: '_$sSh8_VariantV6insertySb8inserted_x17memberAfterInserttxnFSS_Tg5', symObjAddr: 0x16EC, symBinAddr: 0x9C9C, symSize: 0x1AC }
  - { offsetInCU: 0x273B, offset: 0x5EB30, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV9insertNew_2at8isUniqueyxn_s10_HashTableV6BucketVSbtFSS_Tg5', symObjAddr: 0x1898, symBinAddr: 0x9E48, symSize: 0x1B4 }
  - { offsetInCU: 0x2825, offset: 0x5EC1A, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV4copyyyFSS_Tg5', symObjAddr: 0x1A4C, symBinAddr: 0x9FFC, symSize: 0x198 }
  - { offsetInCU: 0x289D, offset: 0x5EC92, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV13copyAndResize8capacityySi_tFSS_Tg5', symObjAddr: 0x1BE4, symBinAddr: 0xA194, symSize: 0x278 }
  - { offsetInCU: 0x2948, offset: 0x5ED3D, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV6resize8capacityySi_tFSS_Tg5', symObjAddr: 0x1E5C, symBinAddr: 0xA40C, symSize: 0x29C }
...
