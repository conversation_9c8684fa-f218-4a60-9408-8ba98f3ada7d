---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Desktop/VS code file/News_collector_UI/news_collector_app/build/ios/Release-iphoneos/path_provider_foundation/path_provider_foundation.framework/path_provider_foundation'
relocations:
  - { offsetInCU: 0x34, offset: 0x57DDE, size: 0x8, addend: 0x0, symName: _path_provider_foundationVersionString, symObjAddr: 0x0, symBinAddr: 0x8340, symSize: 0x0 }
  - { offsetInCU: 0x69, offset: 0x57E13, size: 0x8, addend: 0x0, symName: _path_provider_foundationVersionNumber, symObjAddr: 0x38, symBinAddr: 0x8378, symSize: 0x0 }
  - { offsetInCU: 0x4F, offset: 0x57E78, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation19messagesPigeonCodecC6sharedACvpZ', symObjAddr: 0xAF98, symBinAddr: 0xDFB8, symSize: 0x0 }
  - { offsetInCU: 0xA2, offset: 0x57ECB, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation11PigeonErrorCMa', symObjAddr: 0x3C, symBinAddr: 0x403C, symSize: 0x20 }
  - { offsetInCU: 0xB6, offset: 0x57EDF, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation10nilOrValue33_7EDA05D020E1F315CC375C7C1D72F461LLyxSgypSglFSi_Tg5', symObjAddr: 0x6C, symBinAddr: 0x406C, symSize: 0xEC }
  - { offsetInCU: 0x115, offset: 0x57F3E, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeOSYAASY8rawValuexSg03RawG0Qz_tcfCTW', symObjAddr: 0x218, symBinAddr: 0x4218, symSize: 0x28 }
  - { offsetInCU: 0x146, offset: 0x57F6F, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeOSYAASY8rawValue03RawG0QzvgTW', symObjAddr: 0x240, symBinAddr: 0x4240, symSize: 0xC }
  - { offsetInCU: 0x17C, offset: 0x57FA5, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeO8rawValueACSgSi_tcfCTf4nd_n', symObjAddr: 0x1018, symBinAddr: 0x5018, symSize: 0x10 }
  - { offsetInCU: 0x1E9, offset: 0x58012, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation25messagesPigeonCodecReader33_7EDA05D020E1F315CC375C7C1D72F461LLC9readValue6ofTypeypSgs5UInt8V_tFTo', symObjAddr: 0x3CC, symBinAddr: 0x43CC, symSize: 0xD0 }
  - { offsetInCU: 0x220, offset: 0x58049, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation25messagesPigeonCodecReader33_7EDA05D020E1F315CC375C7C1D72F461LLC4dataAD10Foundation4DataV_tcfcTo', symObjAddr: 0x49C, symBinAddr: 0x449C, symSize: 0xA0 }
  - { offsetInCU: 0x2A8, offset: 0x580D1, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation25messagesPigeonCodecWriter33_7EDA05D020E1F315CC375C7C1D72F461LLC10writeValueyyypFTo', symObjAddr: 0x674, symBinAddr: 0x4674, symSize: 0x68 }
  - { offsetInCU: 0x2DF, offset: 0x58108, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation25messagesPigeonCodecWriter33_7EDA05D020E1F315CC375C7C1D72F461LLC4dataADSo13NSMutableDataC_tcfcTo', symObjAddr: 0x6DC, symBinAddr: 0x46DC, symSize: 0x48 }
  - { offsetInCU: 0x365, offset: 0x5818E, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation31messagesPigeonCodecReaderWriter33_7EDA05D020E1F315CC375C7C1D72F461LLC6reader4withSo015FlutterStandardG0C10Foundation4DataV_tFTo', symObjAddr: 0x764, symBinAddr: 0x4764, symSize: 0xD0 }
  - { offsetInCU: 0x3CA, offset: 0x581F3, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation31messagesPigeonCodecReaderWriter33_7EDA05D020E1F315CC375C7C1D72F461LLC6writer4withSo015FlutterStandardH0CSo13NSMutableDataC_tFTo', symObjAddr: 0x834, symBinAddr: 0x4834, symSize: 0x38 }
  - { offsetInCU: 0x43E, offset: 0x58267, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation25messagesPigeonCodecReader33_7EDA05D020E1F315CC375C7C1D72F461LLCMa', symObjAddr: 0x55C, symBinAddr: 0x455C, symSize: 0x20 }
  - { offsetInCU: 0x452, offset: 0x5827B, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation25messagesPigeonCodecWriter33_7EDA05D020E1F315CC375C7C1D72F461LLCMa', symObjAddr: 0x744, symBinAddr: 0x4744, symSize: 0x20 }
  - { offsetInCU: 0x466, offset: 0x5828F, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation31messagesPigeonCodecReaderWriter33_7EDA05D020E1F315CC375C7C1D72F461LLCMa', symObjAddr: 0x88C, symBinAddr: 0x488C, symSize: 0x20 }
  - { offsetInCU: 0x47A, offset: 0x582A3, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation19messagesPigeonCodecC6shared_WZ', symObjAddr: 0x8AC, symBinAddr: 0x48AC, symSize: 0x70 }
  - { offsetInCU: 0x4BF, offset: 0x582E8, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation19messagesPigeonCodecCfETo', symObjAddr: 0x9A4, symBinAddr: 0x49A4, symSize: 0x4 }
  - { offsetInCU: 0x4EA, offset: 0x58313, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation19messagesPigeonCodecCMa', symObjAddr: 0x9A8, symBinAddr: 0x49A8, symSize: 0x20 }
  - { offsetInCU: 0x607, offset: 0x58430, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation20PathProviderApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryK0_p_AA0deF0_pSgSStFZyypSg_yAKctcfU_', symObjAddr: 0x9C8, symBinAddr: 0x49C8, symSize: 0x180 }
  - { offsetInCU: 0x79B, offset: 0x585C4, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation20PathProviderApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryK0_p_AA0deF0_pSgSStFZyypSg_yAKctcfU0_', symObjAddr: 0xCE8, symBinAddr: 0x4CE8, symSize: 0x190 }
  - { offsetInCU: 0x95F, offset: 0x58788, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation20PathProviderApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryK0_p_AA0deF0_pSgSStFZTf4nnnd_n', symObjAddr: 0x1074, symBinAddr: 0x5074, symSize: 0x444 }
  - { offsetInCU: 0xC59, offset: 0x58A82, size: 0x8, addend: 0x0, symName: '_$sypSgAAIegn_Iegng_yXlSgABIeyBy_IeyByy_TR', symObjAddr: 0xB48, symBinAddr: 0x4B48, symSize: 0xC8 }
  - { offsetInCU: 0xC71, offset: 0x58A9A, size: 0x8, addend: 0x0, symName: '_$syXlSgIeyBy_ypSgIegn_TR', symObjAddr: 0xC10, symBinAddr: 0x4C10, symSize: 0xD8 }
  - { offsetInCU: 0xC89, offset: 0x58AB2, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation20PathProviderApiSetupCMa', symObjAddr: 0xE88, symBinAddr: 0x4E88, symSize: 0x20 }
  - { offsetInCU: 0xC9D, offset: 0x58AC6, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOy', symObjAddr: 0xEA8, symBinAddr: 0x4EA8, symSize: 0x44 }
  - { offsetInCU: 0xCB1, offset: 0x58ADA, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOe', symObjAddr: 0xEEC, symBinAddr: 0x4EEC, symSize: 0x44 }
  - { offsetInCU: 0xCC5, offset: 0x58AEE, size: 0x8, addend: 0x0, symName: '_$sypWOc', symObjAddr: 0xF30, symBinAddr: 0x4F30, symSize: 0x3C }
  - { offsetInCU: 0xCD9, offset: 0x58B02, size: 0x8, addend: 0x0, symName: ___swift_project_boxed_opaque_existential_0, symObjAddr: 0xF6C, symBinAddr: 0x4F6C, symSize: 0x24 }
  - { offsetInCU: 0xCED, offset: 0x58B16, size: 0x8, addend: 0x0, symName: '_$sypSgWOb', symObjAddr: 0xF90, symBinAddr: 0x4F90, symSize: 0x48 }
  - { offsetInCU: 0xD01, offset: 0x58B2A, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0xFD8, symBinAddr: 0x4FD8, symSize: 0x40 }
  - { offsetInCU: 0xD15, offset: 0x58B3E, size: 0x8, addend: 0x0, symName: '_$sypWOb', symObjAddr: 0x1028, symBinAddr: 0x5028, symSize: 0x10 }
  - { offsetInCU: 0xD29, offset: 0x58B52, size: 0x8, addend: 0x0, symName: '_$sSo6NSNullCMa', symObjAddr: 0x1038, symBinAddr: 0x5038, symSize: 0x3C }
  - { offsetInCU: 0xDA3, offset: 0x58BCC, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation15PathProviderApi_pWOb', symObjAddr: 0x1538, symBinAddr: 0x5538, symSize: 0x18 }
  - { offsetInCU: 0xDB7, offset: 0x58BE0, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation15PathProviderApi_pWOc', symObjAddr: 0x1550, symBinAddr: 0x5550, symSize: 0x44 }
  - { offsetInCU: 0xDCB, offset: 0x58BF4, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation20PathProviderApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryK0_p_AA0deF0_pSgSStFZyypSg_yAKctcfU0_TA', symObjAddr: 0x15D8, symBinAddr: 0x55D8, symSize: 0x8 }
  - { offsetInCU: 0xDDF, offset: 0x58C08, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x15E0, symBinAddr: 0x55E0, symSize: 0x10 }
  - { offsetInCU: 0xDF3, offset: 0x58C1C, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x15F0, symBinAddr: 0x55F0, symSize: 0x8 }
  - { offsetInCU: 0xE07, offset: 0x58C30, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation20PathProviderApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryK0_p_AA0deF0_pSgSStFZyypSg_yAKctcfU_TA', symObjAddr: 0x15F8, symBinAddr: 0x55F8, symSize: 0x8 }
  - { offsetInCU: 0xE1B, offset: 0x58C44, size: 0x8, addend: 0x0, symName: '_$syXlSgIeyBy_ypSgIegn_TRTA', symObjAddr: 0x1624, symBinAddr: 0x5624, symSize: 0x8 }
  - { offsetInCU: 0xE2F, offset: 0x58C58, size: 0x8, addend: 0x0, symName: ___swift_memcpy1_1, symObjAddr: 0x162C, symBinAddr: 0x562C, symSize: 0xC }
  - { offsetInCU: 0xE43, offset: 0x58C6C, size: 0x8, addend: 0x0, symName: ___swift_noop_void_return, symObjAddr: 0x1638, symBinAddr: 0x5638, symSize: 0x4 }
  - { offsetInCU: 0xE57, offset: 0x58C80, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeOwet', symObjAddr: 0x163C, symBinAddr: 0x563C, symSize: 0x90 }
  - { offsetInCU: 0xE6B, offset: 0x58C94, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeOwst', symObjAddr: 0x16CC, symBinAddr: 0x56CC, symSize: 0xBC }
  - { offsetInCU: 0xE7F, offset: 0x58CA8, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeOwug', symObjAddr: 0x1788, symBinAddr: 0x5788, symSize: 0x8 }
  - { offsetInCU: 0xE93, offset: 0x58CBC, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeOwup', symObjAddr: 0x1790, symBinAddr: 0x5790, symSize: 0x4 }
  - { offsetInCU: 0xEA7, offset: 0x58CD0, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeOwui', symObjAddr: 0x1794, symBinAddr: 0x5794, symSize: 0x8 }
  - { offsetInCU: 0xEBB, offset: 0x58CE4, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeOMa', symObjAddr: 0x179C, symBinAddr: 0x579C, symSize: 0x10 }
  - { offsetInCU: 0xECF, offset: 0x58CF8, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeOSHAASQWb', symObjAddr: 0x17AC, symBinAddr: 0x57AC, symSize: 0x4 }
  - { offsetInCU: 0xEE3, offset: 0x58D0C, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeOACSQAAWl', symObjAddr: 0x17B0, symBinAddr: 0x57B0, symSize: 0x44 }
  - { offsetInCU: 0xF1C, offset: 0x58D45, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation11PigeonErrorCs0E0AAsADP7_domainSSvgTW', symObjAddr: 0x5C, symBinAddr: 0x405C, symSize: 0x4 }
  - { offsetInCU: 0xF38, offset: 0x58D61, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation11PigeonErrorCs0E0AAsADP5_codeSivgTW', symObjAddr: 0x60, symBinAddr: 0x4060, symSize: 0x4 }
  - { offsetInCU: 0xF54, offset: 0x58D7D, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation11PigeonErrorCs0E0AAsADP9_userInfoyXlSgvgTW', symObjAddr: 0x64, symBinAddr: 0x4064, symSize: 0x4 }
  - { offsetInCU: 0xF70, offset: 0x58D99, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation11PigeonErrorCs0E0AAsADP19_getEmbeddedNSErroryXlSgyFTW', symObjAddr: 0x68, symBinAddr: 0x4068, symSize: 0x4 }
  - { offsetInCU: 0xFA1, offset: 0x58DCA, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x158, symBinAddr: 0x4158, symSize: 0x14 }
  - { offsetInCU: 0x105D, offset: 0x58E86, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeOSHAASH9hashValueSivgTW', symObjAddr: 0x16C, symBinAddr: 0x416C, symSize: 0x44 }
  - { offsetInCU: 0x110C, offset: 0x58F35, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x1B0, symBinAddr: 0x41B0, symSize: 0x28 }
  - { offsetInCU: 0x115F, offset: 0x58F88, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation13DirectoryTypeOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x1D8, symBinAddr: 0x41D8, symSize: 0x40 }
  - { offsetInCU: 0x14B3, offset: 0x592DC, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation11PigeonErrorCfD', symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0x3C }
  - { offsetInCU: 0x151A, offset: 0x59343, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation25messagesPigeonCodecReader33_7EDA05D020E1F315CC375C7C1D72F461LLC9readValue6ofTypeypSgs5UInt8V_tF', symObjAddr: 0x24C, symBinAddr: 0x424C, symSize: 0x180 }
  - { offsetInCU: 0x15F0, offset: 0x59419, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation25messagesPigeonCodecWriter33_7EDA05D020E1F315CC375C7C1D72F461LLC10writeValueyyypF', symObjAddr: 0x57C, symBinAddr: 0x457C, symSize: 0xF8 }
  - { offsetInCU: 0x1731, offset: 0x5955A, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation20PathProviderApiSetupCfD', symObjAddr: 0xE78, symBinAddr: 0x4E78, symSize: 0x10 }
  - { offsetInCU: 0x2B, offset: 0x59655, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation18PathProviderPluginC8register4withySo07FlutterF9Registrar_p_tFZ', symObjAddr: 0x0, symBinAddr: 0x5800, symSize: 0xB4 }
  - { offsetInCU: 0xD8, offset: 0x59702, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation18PathProviderPluginC8register4withySo07FlutterF9Registrar_p_tFZTo', symObjAddr: 0xF4, symBinAddr: 0x58F4, symSize: 0xCC }
  - { offsetInCU: 0x16E, offset: 0x59798, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation18PathProviderPluginCACycfcTo', symObjAddr: 0x1F0, symBinAddr: 0x59F0, symSize: 0x3C }
  - { offsetInCU: 0x1BF, offset: 0x597E9, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation18PathProviderPluginC012getDirectoryD04typeSSSgAA0H4TypeO_tFTf4nd_n', symObjAddr: 0x29C, symBinAddr: 0x5A5C, symSize: 0x90 }
  - { offsetInCU: 0x2E0, offset: 0x5990A, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation18PathProviderPluginC012getContainerD018appGroupIdentifierSSSgSS_tFTf4nd_n', symObjAddr: 0x32C, symBinAddr: 0x5AEC, symSize: 0x1BC }
  - { offsetInCU: 0x33B, offset: 0x59965, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation18PathProviderPluginCMa', symObjAddr: 0xD4, symBinAddr: 0x58D4, symSize: 0x20 }
  - { offsetInCU: 0x414, offset: 0x59A3E, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgWOb', symObjAddr: 0x4E8, symBinAddr: 0x5CA8, symSize: 0x48 }
  - { offsetInCU: 0x575, offset: 0x59B9F, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation18PathProviderPluginC8register4withySo07FlutterF9Registrar_p_tFZ', symObjAddr: 0x0, symBinAddr: 0x5800, symSize: 0xB4 }
  - { offsetInCU: 0x5F1, offset: 0x59C1B, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation18PathProviderPluginCACycfC', symObjAddr: 0xB4, symBinAddr: 0x58B4, symSize: 0x20 }
  - { offsetInCU: 0x60C, offset: 0x59C36, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation18PathProviderPluginCACycfc', symObjAddr: 0x1C0, symBinAddr: 0x59C0, symSize: 0x30 }
  - { offsetInCU: 0x63D, offset: 0x59C67, size: 0x8, addend: 0x0, symName: '_$s24path_provider_foundation18PathProviderPluginCfD', symObjAddr: 0x22C, symBinAddr: 0x5A2C, symSize: 0x30 }
...
