---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Desktop/VS code file/News_collector_UI/news_collector_app/build/ios/Release-iphoneos/flutter_secure_storage/flutter_secure_storage.framework/flutter_secure_storage'
relocations:
  - { offsetInCU: 0x34, offset: 0x5B18A, size: 0x8, addend: 0x0, symName: _flutter_secure_storageVersionString, symObjAddr: 0x0, symBinAddr: 0x10D30, symSize: 0x0 }
  - { offsetInCU: 0x69, offset: 0x5B1BF, size: 0x8, addend: 0x0, symName: _flutter_secure_storageVersionNumber, symObjAddr: 0x38, symBinAddr: 0x10D68, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x5B1FC, size: 0x8, addend: 0x0, symName: '+[FlutterSecureStoragePlugin registerWithRegistrar:]', symObjAddr: 0x0, symBinAddr: 0x8000, symSize: 0xC }
  - { offsetInCU: 0x91, offset: 0x5B266, size: 0x8, addend: 0x0, symName: '+[FlutterSecureStoragePlugin registerWithRegistrar:]', symObjAddr: 0x0, symBinAddr: 0x8000, symSize: 0xC }
  - { offsetInCU: 0xAB, offset: 0x5B37E, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage20FlutterSecureStorageC4read3key7groupId11accountNameAA0deF8ResponseVSS_SSSgAJtF0G5ValueL_14synchronizableAISbSg_tF', symObjAddr: 0x6B0, symBinAddr: 0x86BC, symSize: 0x244 }
  - { offsetInCU: 0x28E, offset: 0x5B561, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage20FlutterSecureStorageC19parseAccessibleAttr33_021DA6E96A14FF6BB30164559D49C116LL13accessibilitySo11CFStringRefaSSSg_tFTf4nd_g', symObjAddr: 0x2378, symBinAddr: 0xA384, symSize: 0x1F8 }
  - { offsetInCU: 0x3A7, offset: 0x5B67A, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage20FlutterSecureStorageC9baseQuery33_021DA6E96A14FF6BB30164559D49C116LL3key7groupId11accountName14synchronizable13accessibility10returnDataSDySo11CFStringRefaypGSSSg_A2OSbSgAoPtFTf4nnnnnnd_n', symObjAddr: 0x2678, symBinAddr: 0xA684, symSize: 0x51C }
  - { offsetInCU: 0x783, offset: 0x5BA56, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage20FlutterSecureStorageC11containsKey3key7groupId11accountNames6ResultOySbAA10OSSecErrorVGSS_SSSgAMtFTf4nnnd_n', symObjAddr: 0x2DB0, symBinAddr: 0xADBC, symSize: 0x18C }
  - { offsetInCU: 0xB33, offset: 0x5BE06, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage31SwiftFlutterSecureStoragePluginC0afG7Manager33_B74E66EA249BA36A2DD8E4B228F6890BLLAA0efG0Cvpfi', symObjAddr: 0xFA4, symBinAddr: 0x8FB0, symSize: 0x1C }
  - { offsetInCU: 0xB61, offset: 0x5BE34, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage20FlutterSecureStorageCMa', symObjAddr: 0xFC0, symBinAddr: 0x8FCC, symSize: 0x20 }
  - { offsetInCU: 0xB75, offset: 0x5BE48, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage31SwiftFlutterSecureStoragePluginC24secStoreAvailabilitySink33_B74E66EA249BA36A2DD8E4B228F6890BLLyypSgcSgvpfi', symObjAddr: 0xFE0, symBinAddr: 0x8FEC, symSize: 0xC }
  - { offsetInCU: 0x1095, offset: 0x5C368, size: 0x8, addend: 0x0, symName: '_$sS2SSysWl', symObjAddr: 0x2F3C, symBinAddr: 0xAF48, symSize: 0x44 }
  - { offsetInCU: 0x10A9, offset: 0x5C37C, size: 0x8, addend: 0x0, symName: '_$sypSgWOh', symObjAddr: 0x2F80, symBinAddr: 0xAF8C, symSize: 0x40 }
  - { offsetInCU: 0x10BD, offset: 0x5C390, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0x2FC0, symBinAddr: 0xAFCC, symSize: 0x40 }
  - { offsetInCU: 0x10D1, offset: 0x5C3A4, size: 0x8, addend: 0x0, symName: '_$sSo11CFStringRefaMa', symObjAddr: 0x3000, symBinAddr: 0xB00C, symSize: 0x54 }
  - { offsetInCU: 0x10E5, offset: 0x5C3B8, size: 0x8, addend: 0x0, symName: '_$sypWOb', symObjAddr: 0x3054, symBinAddr: 0xB060, symSize: 0x10 }
  - { offsetInCU: 0x10F9, offset: 0x5C3CC, size: 0x8, addend: 0x0, symName: ___swift_destroy_boxed_opaque_existential_0, symObjAddr: 0x3064, symBinAddr: 0xB070, symSize: 0x20 }
  - { offsetInCU: 0x110D, offset: 0x5C3E0, size: 0x8, addend: 0x0, symName: '_$sypSgWOb', symObjAddr: 0x3084, symBinAddr: 0xB090, symSize: 0x48 }
  - { offsetInCU: 0x1121, offset: 0x5C3F4, size: 0x8, addend: 0x0, symName: '_$sypWOc', symObjAddr: 0x30CC, symBinAddr: 0xB0D8, symSize: 0x3C }
  - { offsetInCU: 0x1135, offset: 0x5C408, size: 0x8, addend: 0x0, symName: '_$sSo12NSDictionaryCMa', symObjAddr: 0x314C, symBinAddr: 0xB158, symSize: 0x3C }
  - { offsetInCU: 0x1149, offset: 0x5C41C, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOe', symObjAddr: 0x3188, symBinAddr: 0xB194, symSize: 0x44 }
  - { offsetInCU: 0x115D, offset: 0x5C430, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage28FlutterSecureStorageResponseVwCP', symObjAddr: 0x31CC, symBinAddr: 0xB1D8, symSize: 0x30 }
  - { offsetInCU: 0x1171, offset: 0x5C444, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage28FlutterSecureStorageResponseVwxx', symObjAddr: 0x31FC, symBinAddr: 0xB208, symSize: 0x14 }
  - { offsetInCU: 0x1185, offset: 0x5C458, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage28FlutterSecureStorageResponseVwcp', symObjAddr: 0x3210, symBinAddr: 0xB21C, symSize: 0x60 }
  - { offsetInCU: 0x1199, offset: 0x5C46C, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage28FlutterSecureStorageResponseVwca', symObjAddr: 0x3270, symBinAddr: 0xB27C, symSize: 0x90 }
  - { offsetInCU: 0x11AD, offset: 0x5C480, size: 0x8, addend: 0x0, symName: ___swift_assign_boxed_opaque_existential_0, symObjAddr: 0x3300, symBinAddr: 0xB30C, symSize: 0x160 }
  - { offsetInCU: 0x11C1, offset: 0x5C494, size: 0x8, addend: 0x0, symName: ___swift_memcpy40_8, symObjAddr: 0x3460, symBinAddr: 0xB46C, symSize: 0x14 }
  - { offsetInCU: 0x11D5, offset: 0x5C4A8, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage28FlutterSecureStorageResponseVwta', symObjAddr: 0x3474, symBinAddr: 0xB480, symSize: 0x5C }
  - { offsetInCU: 0x11E9, offset: 0x5C4BC, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage28FlutterSecureStorageResponseVwet', symObjAddr: 0x34D0, symBinAddr: 0xB4DC, symSize: 0x5C }
  - { offsetInCU: 0x11FD, offset: 0x5C4D0, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage28FlutterSecureStorageResponseVwst', symObjAddr: 0x352C, symBinAddr: 0xB538, symSize: 0x5C }
  - { offsetInCU: 0x1211, offset: 0x5C4E4, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage28FlutterSecureStorageResponseVMa', symObjAddr: 0x3588, symBinAddr: 0xB594, symSize: 0x10 }
  - { offsetInCU: 0x1225, offset: 0x5C4F8, size: 0x8, addend: 0x0, symName: '_$sSo11CFStringRefa14CoreFoundation9_CFObjectSCSHWb', symObjAddr: 0x3598, symBinAddr: 0xB5A4, symSize: 0x2C }
  - { offsetInCU: 0x1239, offset: 0x5C50C, size: 0x8, addend: 0x0, symName: '_$sSo11CFStringRefaSHSCSQWb', symObjAddr: 0x35C4, symBinAddr: 0xB5D0, symSize: 0x2C }
  - { offsetInCU: 0x144B, offset: 0x5C71E, size: 0x8, addend: 0x0, symName: '_$sSo11CFStringRefaSHSCSH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x1070, symBinAddr: 0x907C, symSize: 0x58 }
  - { offsetInCU: 0x14EF, offset: 0x5C7C2, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSo11CFStringRefa_ypTgm5Tf4g_n', symObjAddr: 0x2570, symBinAddr: 0xA57C, symSize: 0x108 }
  - { offsetInCU: 0x1640, offset: 0x5C913, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SSTgm5Tf4g_n', symObjAddr: 0x2B94, symBinAddr: 0xABA0, symSize: 0x114 }
  - { offsetInCU: 0x176D, offset: 0x5CA40, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSo11CFStringRefa_ypSgTgm5Tf4g_n', symObjAddr: 0x2CA8, symBinAddr: 0xACB4, symSize: 0x108 }
  - { offsetInCU: 0x1A15, offset: 0x5CCE8, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage20FlutterSecureStorageC7readAll7groupId11accountName14synchronizable13accessibilityAA0deF8ResponseVSSSg_AKSbSgAKtF', symObjAddr: 0x0, symBinAddr: 0x800C, symSize: 0x458 }
  - { offsetInCU: 0x1C04, offset: 0x5CED7, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage20FlutterSecureStorageC7readAll7groupId11accountName14synchronizable13accessibilityAA0deF8ResponseVSSSg_AKSbSgAKtFyypXEfU_', symObjAddr: 0x458, symBinAddr: 0x8464, symSize: 0x258 }
  - { offsetInCU: 0x1E25, offset: 0x5D0F8, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage20FlutterSecureStorageC5write3key5value7groupId11accountName14synchronizable13accessibilityAA0deF8ResponseVSS_S2SSgAMSbSgAMtF', symObjAddr: 0x8F4, symBinAddr: 0x8900, symSize: 0x6A0 }
  - { offsetInCU: 0x229E, offset: 0x5D571, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage20FlutterSecureStorageCfD', symObjAddr: 0xF94, symBinAddr: 0x8FA0, symSize: 0x10 }
  - { offsetInCU: 0x22DD, offset: 0x5D5B0, size: 0x8, addend: 0x0, symName: '_$sSo11CFStringRefaSHSCSH9hashValueSivgTW', symObjAddr: 0xFEC, symBinAddr: 0x8FF8, symSize: 0x3C }
  - { offsetInCU: 0x22F9, offset: 0x5D5CC, size: 0x8, addend: 0x0, symName: '_$sSo11CFStringRefaSHSCSH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x1028, symBinAddr: 0x9034, symSize: 0x48 }
  - { offsetInCU: 0x231C, offset: 0x5D5EF, size: 0x8, addend: 0x0, symName: '_$sSo11CFStringRefaSQSCSQ2eeoiySbx_xtFZTW', symObjAddr: 0x10C8, symBinAddr: 0x90D4, symSize: 0x4C }
  - { offsetInCU: 0x2371, offset: 0x5D644, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSo11CFStringRefa_Tg5', symObjAddr: 0x1114, symBinAddr: 0x9120, symSize: 0x88 }
  - { offsetInCU: 0x23CB, offset: 0x5D69E, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSS_Tg5', symObjAddr: 0x119C, symBinAddr: 0x91A8, symSize: 0x64 }
  - { offsetInCU: 0x241E, offset: 0x5D6F1, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSo11CFStringRefa_ypTg5', symObjAddr: 0x1200, symBinAddr: 0x920C, symSize: 0x68 }
  - { offsetInCU: 0x24C1, offset: 0x5D794, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFSS_SSTg5', symObjAddr: 0x1268, symBinAddr: 0x9274, symSize: 0xF0 }
  - { offsetInCU: 0x2590, offset: 0x5D863, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV12mutatingFind_8isUniques10_HashTableV6BucketV6bucket_Sb5foundtx_SbtFSo11CFStringRefa_ypTg5', symObjAddr: 0x1358, symBinAddr: 0x9364, symSize: 0xC8 }
  - { offsetInCU: 0x25D4, offset: 0x5D8A7, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV12mutatingFind_8isUniques10_HashTableV6BucketV6bucket_Sb5foundtx_SbtFSS_SSTg5', symObjAddr: 0x1420, symBinAddr: 0x942C, symSize: 0xD8 }
  - { offsetInCU: 0x2611, offset: 0x5D8E4, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSo11CFStringRefa_Tg5', symObjAddr: 0x14F8, symBinAddr: 0x9504, symSize: 0x14C }
  - { offsetInCU: 0x263F, offset: 0x5D912, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSS_Tg5', symObjAddr: 0x1644, symBinAddr: 0x9650, symSize: 0xE0 }
  - { offsetInCU: 0x26BA, offset: 0x5D98D, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSo11CFStringRefa_ypTg5', symObjAddr: 0x1724, symBinAddr: 0x9730, symSize: 0x1D8 }
  - { offsetInCU: 0x2753, offset: 0x5DA26, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_SSTg5', symObjAddr: 0x18FC, symBinAddr: 0x9908, symSize: 0x1C8 }
  - { offsetInCU: 0x2808, offset: 0x5DADB, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSo11CFStringRefa_ypTg5', symObjAddr: 0x1AC4, symBinAddr: 0x9AD0, symSize: 0x34C }
  - { offsetInCU: 0x2914, offset: 0x5DBE7, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_SSTg5', symObjAddr: 0x1E10, symBinAddr: 0x9E1C, symSize: 0x354 }
  - { offsetInCU: 0x29FE, offset: 0x5DCD1, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFSo11CFStringRefa_ypTg5', symObjAddr: 0x2164, symBinAddr: 0xA170, symSize: 0x214 }
  - { offsetInCU: 0x7E, offset: 0x5DEF6, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage31SwiftFlutterSecureStoragePluginC8register4withySo0eH9Registrar_p_tFZTo', symObjAddr: 0x24, symBinAddr: 0xB660, symSize: 0x30 }
  - { offsetInCU: 0x1CB, offset: 0x5E043, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage31SwiftFlutterSecureStoragePluginC6handle_6resultySo0E10MethodCallC_yypSgctFTo', symObjAddr: 0x10E0, symBinAddr: 0xC71C, symSize: 0x90 }
  - { offsetInCU: 0x222, offset: 0x5E09A, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage31SwiftFlutterSecureStoragePluginC8onListen13withArguments9eventSinkSo0E5ErrorCSgypSg_yAJctFTo', symObjAddr: 0x1308, symBinAddr: 0xC944, symSize: 0xE0 }
  - { offsetInCU: 0x2B4, offset: 0x5E12C, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage31SwiftFlutterSecureStoragePluginC8onCancel13withArgumentsSo0E5ErrorCSgypSg_tFTo', symObjAddr: 0x1414, symBinAddr: 0xCA50, symSize: 0x94 }
  - { offsetInCU: 0x337, offset: 0x5E1AF, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage31SwiftFlutterSecureStoragePluginCACycfcTo', symObjAddr: 0x1510, symBinAddr: 0xCB4C, symSize: 0x6C }
  - { offsetInCU: 0x39D, offset: 0x5E215, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage31SwiftFlutterSecureStoragePluginC8register4withySo0eH9Registrar_p_tFZTf4nd_n', symObjAddr: 0x15E8, symBinAddr: 0xCC24, symSize: 0x184 }
  - { offsetInCU: 0x45A, offset: 0x5E2D2, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage31SwiftFlutterSecureStoragePluginC9parseCall33_B74E66EA249BA36A2DD8E4B228F6890BLLyAC0efG7RequestVSo0e6MethodJ0CFTf4nd_n', symObjAddr: 0x196C, symBinAddr: 0xCF48, symSize: 0x7F0 }
  - { offsetInCU: 0x6AE, offset: 0x5E526, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage31SwiftFlutterSecureStoragePluginC14handleResponse33_B74E66EA249BA36A2DD8E4B228F6890BLLyyAA0efgJ0V_yypSgctF012$s22flutter_b10_storage31defgH59C6handle_6resultySo0E10MethodCallC_yypSgctF0I6ResultL_yyAHFAHIegn_Tf1ncn_nTf4ndg_n', symObjAddr: 0x215C, symBinAddr: 0xD738, symSize: 0x308 }
  - { offsetInCU: 0x7F0, offset: 0x5E668, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage31SwiftFlutterSecureStoragePluginC5write33_B74E66EA249BA36A2DD8E4B228F6890BLLyySo0E10MethodCallC_yypSgctF012$s22flutter_b10_storage31defgh23C6handle_6resultySo0E10sT26C_yypSgctF0I6ResultL_yyAHFAHIegn_Tf1ncn_nTf4nng_n', symObjAddr: 0x2464, symBinAddr: 0xDA40, symSize: 0x434 }
  - { offsetInCU: 0x8F9, offset: 0x5E771, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage31SwiftFlutterSecureStoragePluginC11containsKey33_B74E66EA249BA36A2DD8E4B228F6890BLLyySo0E10MethodCallC_yypSgctF012$s22flutter_b10_storage31defgh23C6handle_6resultySo0E10tU26C_yypSgctF0I6ResultL_yyAHFAHIegn_Tf1ncn_nTf4ndg_n', symObjAddr: 0x2898, symBinAddr: 0xDE74, symSize: 0x3DC }
  - { offsetInCU: 0xC00, offset: 0x5EA78, size: 0x8, addend: 0x0, symName: '_$syXlSgIeyBy_ypSgIegn_TR', symObjAddr: 0x1170, symBinAddr: 0xC7AC, symSize: 0xD0 }
  - { offsetInCU: 0xC18, offset: 0x5EA90, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage31SwiftFlutterSecureStoragePluginCfETo', symObjAddr: 0x15AC, symBinAddr: 0xCBE8, symSize: 0x3C }
  - { offsetInCU: 0xC47, offset: 0x5EABF, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage31SwiftFlutterSecureStoragePluginC6handle_6resultySo0E10MethodCallC_yypSgctFyyYbcfU_TA', symObjAddr: 0x17A0, symBinAddr: 0xCDDC, symSize: 0xC }
  - { offsetInCU: 0xC5B, offset: 0x5EAD3, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x17AC, symBinAddr: 0xCDE8, symSize: 0x10 }
  - { offsetInCU: 0xC6F, offset: 0x5EAE7, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x17BC, symBinAddr: 0xCDF8, symSize: 0x8 }
  - { offsetInCU: 0xC83, offset: 0x5EAFB, size: 0x8, addend: 0x0, symName: '_$sSay8Dispatch0A13WorkItemFlagsVGMa', symObjAddr: 0x1804, symBinAddr: 0xCE00, symSize: 0x54 }
  - { offsetInCU: 0xC97, offset: 0x5EB0F, size: 0x8, addend: 0x0, symName: '_$sypSgIegn_SgWOe', symObjAddr: 0x1858, symBinAddr: 0xCE54, symSize: 0x10 }
  - { offsetInCU: 0xCAB, offset: 0x5EB23, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage31SwiftFlutterSecureStoragePluginCMa', symObjAddr: 0x1868, symBinAddr: 0xCE64, symSize: 0x20 }
  - { offsetInCU: 0xCBF, offset: 0x5EB37, size: 0x8, addend: 0x0, symName: '_$syXlSgIeyBy_ypSgIegn_TRTA', symObjAddr: 0x18AC, symBinAddr: 0xCEA8, symSize: 0x8 }
  - { offsetInCU: 0xCD3, offset: 0x5EB4B, size: 0x8, addend: 0x0, symName: '_$sypSgWOc', symObjAddr: 0x18B4, symBinAddr: 0xCEB0, symSize: 0x48 }
  - { offsetInCU: 0xCE7, offset: 0x5EB5F, size: 0x8, addend: 0x0, symName: ___swift_project_boxed_opaque_existential_0, symObjAddr: 0x18FC, symBinAddr: 0xCEF8, symSize: 0x24 }
  - { offsetInCU: 0xCFB, offset: 0x5EB73, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage31SwiftFlutterSecureStoragePluginC6handle_6resultySo0E10MethodCallC_yypSgctFyyYbcfU_yyScMYccfU_TA', symObjAddr: 0x1964, symBinAddr: 0xCF40, symSize: 0x8 }
  - { offsetInCU: 0xD34, offset: 0x5EBAC, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage28FlutterSecureStorageResponseVWOh', symObjAddr: 0x2C74, symBinAddr: 0xE250, symSize: 0x34 }
  - { offsetInCU: 0xD48, offset: 0x5EBC0, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage31SwiftFlutterSecureStoragePluginC0efG7RequestVWOs', symObjAddr: 0x2CA8, symBinAddr: 0xE284, symSize: 0x68 }
  - { offsetInCU: 0xD5C, offset: 0x5EBD4, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage31SwiftFlutterSecureStoragePluginC6handle_6resultySo0E10MethodCallC_yypSgctF0I6ResultL_yyAHFyyScMYccfU_TA', symObjAddr: 0x2E50, symBinAddr: 0xE3D4, symSize: 0x28 }
  - { offsetInCU: 0x10A9, offset: 0x5EF21, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage31SwiftFlutterSecureStoragePluginC8register4withySo0eH9Registrar_p_tFZ', symObjAddr: 0x0, symBinAddr: 0xB63C, symSize: 0x4 }
  - { offsetInCU: 0x10BD, offset: 0x5EF35, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage31SwiftFlutterSecureStoragePluginCACycfC', symObjAddr: 0x4, symBinAddr: 0xB640, symSize: 0x20 }
  - { offsetInCU: 0x111C, offset: 0x5EF94, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage31SwiftFlutterSecureStoragePluginC6handle_6resultySo0E10MethodCallC_yypSgctF', symObjAddr: 0x54, symBinAddr: 0xB690, symSize: 0x278 }
  - { offsetInCU: 0x1182, offset: 0x5EFFA, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage31SwiftFlutterSecureStoragePluginC6handle_6resultySo0E10MethodCallC_yypSgctF0I6ResultL_yyAHF', symObjAddr: 0x2CC, symBinAddr: 0xB908, symSize: 0x228 }
  - { offsetInCU: 0x1209, offset: 0x5F081, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage31SwiftFlutterSecureStoragePluginC6handle_6resultySo0E10MethodCallC_yypSgctFyyYbcfU_', symObjAddr: 0x4F8, symBinAddr: 0xBB34, symSize: 0xB24 }
  - { offsetInCU: 0x165E, offset: 0x5F4D6, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage31SwiftFlutterSecureStoragePluginC6handle_6resultySo0E10MethodCallC_yypSgctFyyYbcfU_yyScMYccfU_', symObjAddr: 0x101C, symBinAddr: 0xC658, symSize: 0x94 }
  - { offsetInCU: 0x18E7, offset: 0x5F75F, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage31SwiftFlutterSecureStoragePluginC8onListen13withArguments9eventSinkSo0E5ErrorCSgypSg_yAJctF', symObjAddr: 0x12C4, symBinAddr: 0xC900, symSize: 0x44 }
  - { offsetInCU: 0x1976, offset: 0x5F7EE, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage31SwiftFlutterSecureStoragePluginC8onCancel13withArgumentsSo0E5ErrorCSgypSg_tF', symObjAddr: 0x13E8, symBinAddr: 0xCA24, symSize: 0x2C }
  - { offsetInCU: 0x19EE, offset: 0x5F866, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage31SwiftFlutterSecureStoragePluginCACycfc', symObjAddr: 0x14A8, symBinAddr: 0xCAE4, symSize: 0x68 }
  - { offsetInCU: 0x1A36, offset: 0x5F8AE, size: 0x8, addend: 0x0, symName: '_$s22flutter_secure_storage31SwiftFlutterSecureStoragePluginCfD', symObjAddr: 0x157C, symBinAddr: 0xCBB8, symSize: 0x30 }
...
