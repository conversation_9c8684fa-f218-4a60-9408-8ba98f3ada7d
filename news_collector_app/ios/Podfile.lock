PODS:
  - Flutter (1.0.0)
  - flutter_secure_storage (6.0.0):
    - Flutter
  - integration_test (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - Flutter (from `Flutter`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - integration_test (from `.symlinks/plugins/integration_test/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)

EXTERNAL SOURCES:
  Flutter:
    :path: Flutter
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  integration_test:
    :path: ".symlinks/plugins/integration_test/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"

SPEC CHECKSUMS:
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_secure_storage: d33dac7ae2ea08509be337e775f6b59f1ff45f12
  integration_test: 252f60fa39af5e17c3aa9899d35d908a0721b573
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78

PODFILE CHECKSUM: 4305caec6b40dde0ae97be1573c53de1882a07e5

COCOAPODS: 1.16.2
