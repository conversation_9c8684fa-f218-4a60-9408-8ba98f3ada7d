// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXAggregateTarget section */
		1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 94078A06C17E946ADC1F2C06726219E5 /* Build configuration list for PBXAggregateTarget "Flutter" */;
			buildPhases = (
			);
			dependencies = (
			);
			name = Flutter;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		07E82B2FD0398AD3DD8C065F225532A4 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = B6A123AA1A9854DFFC03245B97D03386 /* PrivacyInfo.xcprivacy */; };
		14F8DD4CD78B04D048D64E6B0C8F4D0B /* path_provider_foundation-path_provider_foundation_privacy in Resources */ = {isa = PBXBuildFile; fileRef = 3DBD4BADE27F8B91024E4B4B4DD75DB5 /* path_provider_foundation-path_provider_foundation_privacy */; };
		2575DFFA510CC2B4234A4D50EA5F0D3F /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 5809C5699B36C6515BA24827691DD4F3 /* PrivacyInfo.xcprivacy */; };
		2631CEC725AA8C0549E5D2980171E0B0 /* path_provider_foundation-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = B08A01A7C65BD083B9CB27CFEE30EAF6 /* path_provider_foundation-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3BC0157C4228588BA0E7D4756DC8ED6B /* IntegrationTestIosTest.m in Sources */ = {isa = PBXBuildFile; fileRef = DA7093F02EA2F146C1C8AEE7472CEBE9 /* IntegrationTestIosTest.m */; };
		406CAFAD6C9D83D830DB57BE4A39D10A /* IntegrationTestIosTest.h in Headers */ = {isa = PBXBuildFile; fileRef = 85FB06C973B6B0C00D6C2741371B3644 /* IntegrationTestIosTest.h */; settings = {ATTRIBUTES = (Public, ); }; };
		43C8655BA99B44C6E8988F3AEA86BC3F /* SharedPreferencesPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 869490557E12485F15232D747E2B917B /* SharedPreferencesPlugin.swift */; };
		48ABC9CC212358D168731AEE56729598 /* shared_preferences_foundation-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = D06E382C6CDB96104070CE3D98EA7F87 /* shared_preferences_foundation-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		569B4672244559E160E56125FCD6E7C6 /* FLTIntegrationTestRunner.h in Headers */ = {isa = PBXBuildFile; fileRef = D50B7242D2425C7E993B4D44DB1CEDCE /* FLTIntegrationTestRunner.h */; settings = {ATTRIBUTES = (Public, ); }; };
		56FD98664C20CE8A5F7358B652BECD7E /* flutter_secure_storage-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 356005FC6DE78797A2695EE823F9C11F /* flutter_secure_storage-dummy.m */; };
		6AF898E6C70727BB0EB0128A6C51BB79 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 485F2108AD79BE45C0D7EB400113BB73 /* PrivacyInfo.xcprivacy */; };
		7E757AF9A0CD9956DBF91D34E60B8788 /* Pods-Runner-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 499E3722E8DACDC2AAA6C15AD1796520 /* Pods-Runner-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7F35898B19881C1CE0B65100F140EABE /* flutter_secure_storage-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = BF98CE53AA0A9F98E9E3CE4381487A0E /* flutter_secure_storage-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		898AE8C3E44E2345DAEAD5B11FEB1A49 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8193349D819536E58C58A34C1B7DF545 /* Foundation.framework */; };
		898F57570FA3E297EA6915712A16B230 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8193349D819536E58C58A34C1B7DF545 /* Foundation.framework */; };
		8A655B581F2FFA1A700C0AA32E29941E /* integration_test-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 71133531204ABAC4D3DAEA3CB35E51E2 /* integration_test-dummy.m */; };
		8C47508E707EAC61A796BC803D08EB26 /* PathProviderPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 53F5E3A35C16FDB0D3A9F2EADD6BE4A9 /* PathProviderPlugin.swift */; };
		8DA6CF83FBECBDD823728B1AD863A0C8 /* FlutterSecureStoragePlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = 2449552EAE401BE4115E2B8C673DD3F2 /* FlutterSecureStoragePlugin.m */; };
		8FA3D968B971C1C6F83C5E2582A9033F /* SwiftFlutterSecureStoragePlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = D6E248DF5C2842C7779A52949C00917F /* SwiftFlutterSecureStoragePlugin.swift */; };
		92D06CE697A81B385ED6CD55784CB620 /* messages.g.swift in Sources */ = {isa = PBXBuildFile; fileRef = FC8B4582EA4ADB2E92011673BA7B2B1C /* messages.g.swift */; };
		A43F424C63EBD85B2861082DEA1BFB84 /* IntegrationTestPlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = 888697E17222CBD04C018B67CB69E7EF /* IntegrationTestPlugin.m */; };
		B375EA128E6579366091BAA390BBDD34 /* Pods-RunnerTests-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 4D3DB54C128E3D88C0C9C7DDEA475ED7 /* Pods-RunnerTests-dummy.m */; };
		B7C67D475D63B3D6501863172F8AD35F /* shared_preferences_foundation-shared_preferences_foundation_privacy in Resources */ = {isa = PBXBuildFile; fileRef = 0652FCE3FC19056983AABE058B3CC45B /* shared_preferences_foundation-shared_preferences_foundation_privacy */; };
		BBEB27BD1ED8774EC3EC3DEDE2F1FEFA /* IntegrationTestPlugin.h in Headers */ = {isa = PBXBuildFile; fileRef = 3B138BC214B1B8A5C0766D34B55F6E6D /* IntegrationTestPlugin.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C0B8533CF680A699DFAC3233AA9FA22C /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8193349D819536E58C58A34C1B7DF545 /* Foundation.framework */; };
		CCA077DFC736EDF948DED65DB4684718 /* FLTIntegrationTestRunner.m in Sources */ = {isa = PBXBuildFile; fileRef = AFCD559CA1BA984ED5BA9B329B9903E5 /* FLTIntegrationTestRunner.m */; };
		CE777A3D5F9030D78EBCEA607A828715 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = ECF4CA3AF5536D14B4E015504E7AEEC0 /* UIKit.framework */; };
		CEBD84922D2CCEF26C272418EC3EB3A6 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8193349D819536E58C58A34C1B7DF545 /* Foundation.framework */; };
		D2A52C6979E7137696C04242485D21EC /* shared_preferences_foundation-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 873F0477514EDF6C041911D3714FFA11 /* shared_preferences_foundation-dummy.m */; };
		D3161FF34F8E2B45AD80A7B9450BE18B /* integration_test-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = B4B5755748E2628F111C9144B8405D63 /* integration_test-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D8203AF03D60E3B7237E39C59F418E53 /* flutter_secure_storage-flutter_secure_storage in Resources */ = {isa = PBXBuildFile; fileRef = E3C330B5F4227ED6FDC9FC042B5115D5 /* flutter_secure_storage-flutter_secure_storage */; };
		DACB447D658FB7CEE4CDA65714EAB43F /* FlutterSecureStoragePlugin.h in Headers */ = {isa = PBXBuildFile; fileRef = E03F62DA0DAC1A423FAC06435FBBAF62 /* FlutterSecureStoragePlugin.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E07748E423DD19D365E1E1D0CB75D3C6 /* messages.g.swift in Sources */ = {isa = PBXBuildFile; fileRef = D8614E246D33BE30FF786DE1AA743E95 /* messages.g.swift */; };
		E6B934071CC8AAC4C2F13418E980DC83 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8193349D819536E58C58A34C1B7DF545 /* Foundation.framework */; };
		E7798C39E72D9BEA2B31DD6A0E00CFEB /* path_provider_foundation-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = BAC33B9913C38E9776028FE57E924002 /* path_provider_foundation-dummy.m */; };
		E8B800C8DB1A0A612E05CE6661CCA6A9 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8193349D819536E58C58A34C1B7DF545 /* Foundation.framework */; };
		E9DF8BE0593966A0EC9E1A0E6933F9DB /* Pods-Runner-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = E794494AB90477BA94C681E994ADF341 /* Pods-Runner-dummy.m */; };
		EB2DC96CDCF638AB89007D2DB0F3119A /* Pods-RunnerTests-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 9E9B12BCE5540D02A80250C3EC16D399 /* Pods-RunnerTests-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		EDD4406148BF8FB8427AE35A9F47C3A5 /* FlutterSecureStorage.swift in Sources */ = {isa = PBXBuildFile; fileRef = F170D551E7111567220ED44EF78572C7 /* FlutterSecureStorage.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		18B21F4F96C1C6E9CC2C9FE00FCD74BB /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		2A3A3FD5575E6D671CB44AFD59FE704E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 15490FE8E4E35B20C7777FA3316A00AA;
			remoteInfo = flutter_secure_storage;
		};
		42C1A90DDFBF3EAC73800E0D3B93B70E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		46E6E8C84A79F1FDC236F3E473DD2F33 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8B74B458B450D74B75744B87BD747314;
			remoteInfo = "Pods-Runner";
		};
		4C9AC6E09E48A18FFBD034B6A397262E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = ADE86197C9EC7D5D7AB212E24AE13395;
			remoteInfo = integration_test;
		};
		62F7F0624CD32DE6EA496D4E739E1F9F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = CACE6618E7996464E38687E13F67D945;
			remoteInfo = "path_provider_foundation-path_provider_foundation_privacy";
		};
		63AA6E7D37193BDB050D1051167AA612 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 56F581DDCB0A032454E604885E17AE3C;
			remoteInfo = path_provider_foundation;
		};
		670B5623A895FC7CACE8A7A1A49C0143 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B6AF8B7CEAF6321719ABBC7E770624DA;
			remoteInfo = "shared_preferences_foundation-shared_preferences_foundation_privacy";
		};
		6B524ACAA4B4A30A1C27A06275FC7443 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = BA5B0F40AD74C7512B1E4FD79F8F202F;
			remoteInfo = "flutter_secure_storage-flutter_secure_storage";
		};
		9C02CF0EBC978B2E1783AF68F90CCBFA /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = AB5EE685B22D01885ADD930538E8DD3C;
			remoteInfo = shared_preferences_foundation;
		};
		B2644DF8D110E38C3547754AD0E10180 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		C574689BF6866CC554DDBEC547313975 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
		FC3AFCB654FEB28BC13945E8F08F3818 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EFDDC32A34D56D411E640A81DCD9E73;
			remoteInfo = Flutter;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		024D2185AA87C3EA58BD549083AD3502 /* shared_preferences_foundation-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "shared_preferences_foundation-prefix.pch"; sourceTree = "<group>"; };
		03E8AD2E8B75443E12D3D3BD12A9900E /* Pods-RunnerTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-RunnerTests.debug.xcconfig"; sourceTree = "<group>"; };
		0652FCE3FC19056983AABE058B3CC45B /* shared_preferences_foundation-shared_preferences_foundation_privacy */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "shared_preferences_foundation-shared_preferences_foundation_privacy"; path = shared_preferences_foundation_privacy.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		0A75BD300C59519EA3ABFB5D75CCDC0D /* flutter_secure_storage.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = flutter_secure_storage.debug.xcconfig; sourceTree = "<group>"; };
		0AFB643DA4919253F749E2836A5AAFDC /* Pods-Runner-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-Runner-Info.plist"; sourceTree = "<group>"; };
		11AA7018F95317D959D9D2E76CBB2377 /* Pods-RunnerTests-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-RunnerTests-acknowledgements.markdown"; sourceTree = "<group>"; };
		130B3DF2AC68DA4C75EF43E0CD0946DA /* shared_preferences_foundation-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "shared_preferences_foundation-Info.plist"; sourceTree = "<group>"; };
		1B136F41D5B5FAE96EA576E768A5771D /* Pods-Runner-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-Runner-acknowledgements.plist"; sourceTree = "<group>"; };
		1B6E7B07080106A083908D87BF2327F2 /* shared_preferences_foundation.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = shared_preferences_foundation.modulemap; sourceTree = "<group>"; };
		1E3F89FC19634A91DE060C1CEE7E0D36 /* shared_preferences_foundation.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = shared_preferences_foundation.podspec; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		2449552EAE401BE4115E2B8C673DD3F2 /* FlutterSecureStoragePlugin.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FlutterSecureStoragePlugin.m; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/ios/Classes/FlutterSecureStoragePlugin.m"; sourceTree = "<group>"; };
		2566BDB90AD8A043A03EB3E43CA05B91 /* flutter_secure_storage.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = flutter_secure_storage.podspec; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/ios/flutter_secure_storage.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		27BB2B51652CEC054A67F299BA15E067 /* ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist"; sourceTree = "<group>"; };
		34B0A9C091ED932F04FD1D3AF160CEB4 /* integration_test.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = integration_test.release.xcconfig; sourceTree = "<group>"; };
		356005FC6DE78797A2695EE823F9C11F /* flutter_secure_storage-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "flutter_secure_storage-dummy.m"; sourceTree = "<group>"; };
		3B138BC214B1B8A5C0766D34B55F6E6D /* IntegrationTestPlugin.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = IntegrationTestPlugin.h; path = ../../../../../../../../../../../../../../../opt/homebrew/Caskroom/flutter/3.32.5/flutter/packages/integration_test/ios/integration_test/Sources/integration_test/include/IntegrationTestPlugin.h; sourceTree = "<group>"; };
		3C29A9A0523675E67F6E9B274BF36B06 /* path_provider_foundation.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = path_provider_foundation.release.xcconfig; sourceTree = "<group>"; };
		3DBD4BADE27F8B91024E4B4B4DD75DB5 /* path_provider_foundation-path_provider_foundation_privacy */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "path_provider_foundation-path_provider_foundation_privacy"; path = path_provider_foundation_privacy.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		3E8A27C782E727490CC28DF6C23CB746 /* ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist"; sourceTree = "<group>"; };
		410328C0734E96B2D80C03B3D59A6F59 /* integration_test.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = integration_test.debug.xcconfig; sourceTree = "<group>"; };
		415DF6EE06CCCB12BEDC901928FF5BEC /* flutter_secure_storage.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = flutter_secure_storage.modulemap; sourceTree = "<group>"; };
		485F2108AD79BE45C0D7EB400113BB73 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/ios/Resources/PrivacyInfo.xcprivacy"; sourceTree = "<group>"; };
		499E3722E8DACDC2AAA6C15AD1796520 /* Pods-Runner-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-Runner-umbrella.h"; sourceTree = "<group>"; };
		4D3DB54C128E3D88C0C9C7DDEA475ED7 /* Pods-RunnerTests-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-RunnerTests-dummy.m"; sourceTree = "<group>"; };
		5228F5FBCFB21D5149988CC4C7366D26 /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/LICENSE"; sourceTree = "<group>"; };
		53F5E3A35C16FDB0D3A9F2EADD6BE4A9 /* PathProviderPlugin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PathProviderPlugin.swift; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/PathProviderPlugin.swift"; sourceTree = "<group>"; };
		5809C5699B36C6515BA24827691DD4F3 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/Resources/PrivacyInfo.xcprivacy"; sourceTree = "<group>"; };
		5B707EA37CBC3DFDABC9D9DFAD54F4BD /* integration_test */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = integration_test; path = integration_test.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		5EF8C76469D7ACFC619D6C69A7C2A8E7 /* shared_preferences_foundation.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = shared_preferences_foundation.debug.xcconfig; sourceTree = "<group>"; };
		669E8F25E1897672BDB80B7EB784DA24 /* Pods-Runner */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-Runner"; path = Pods_Runner.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		6B8FAA5A1A40A9009E9E7F7289903DC4 /* integration_test-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "integration_test-prefix.pch"; sourceTree = "<group>"; };
		6C3345B1B3CAEDF5B03B1F731FDC492E /* Pods-RunnerTests */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-RunnerTests"; path = Pods_RunnerTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		71133531204ABAC4D3DAEA3CB35E51E2 /* integration_test-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "integration_test-dummy.m"; sourceTree = "<group>"; };
		728A5ED1DD60BAC8C8F382A87B976F84 /* Pods-Runner.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-Runner.debug.xcconfig"; sourceTree = "<group>"; };
		773BFCE41424EB8EBB72EF3F6A5FB719 /* Pods-Runner-frameworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-Runner-frameworks.sh"; sourceTree = "<group>"; };
		79F83F4D505FA40C1CD18A8029181D59 /* Pods-RunnerTests-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-RunnerTests-acknowledgements.plist"; sourceTree = "<group>"; };
		7CD70C2DFF395D68D3A6C9DCE0E38D80 /* path_provider_foundation-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "path_provider_foundation-prefix.pch"; sourceTree = "<group>"; };
		7CF8FE195D467394157BBE62DDB462A0 /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE"; sourceTree = "<group>"; };
		8193349D819536E58C58A34C1B7DF545 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework; sourceTree = DEVELOPER_DIR; };
		85FB06C973B6B0C00D6C2741371B3644 /* IntegrationTestIosTest.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = IntegrationTestIosTest.h; path = ../../../../../../../../../../../../../../../opt/homebrew/Caskroom/flutter/3.32.5/flutter/packages/integration_test/ios/integration_test/Sources/integration_test/include/IntegrationTestIosTest.h; sourceTree = "<group>"; };
		869490557E12485F15232D747E2B917B /* SharedPreferencesPlugin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SharedPreferencesPlugin.swift; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/SharedPreferencesPlugin.swift"; sourceTree = "<group>"; };
		873F0477514EDF6C041911D3714FFA11 /* shared_preferences_foundation-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "shared_preferences_foundation-dummy.m"; sourceTree = "<group>"; };
		888697E17222CBD04C018B67CB69E7EF /* IntegrationTestPlugin.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = IntegrationTestPlugin.m; path = ../../../../../../../../../../../../../../opt/homebrew/Caskroom/flutter/3.32.5/flutter/packages/integration_test/ios/integration_test/Sources/integration_test/IntegrationTestPlugin.m; sourceTree = "<group>"; };
		8B261F0229DB316C6D31B1254C453D70 /* flutter_secure_storage-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "flutter_secure_storage-prefix.pch"; sourceTree = "<group>"; };
		912C49F87F0197643EA8696407DB04AB /* integration_test-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "integration_test-Info.plist"; sourceTree = "<group>"; };
		93C6A45C6E4792269BE9BE0073839BF0 /* shared_preferences_foundation */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = shared_preferences_foundation; path = shared_preferences_foundation.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		9D940727FF8FB9C785EB98E56350EF41 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		9E9B12BCE5540D02A80250C3EC16D399 /* Pods-RunnerTests-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-RunnerTests-umbrella.h"; sourceTree = "<group>"; };
		A63E04ED2197F574A1C39A8AC54DC8B5 /* flutter_secure_storage.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = flutter_secure_storage.release.xcconfig; sourceTree = "<group>"; };
		AA6B2500D7C0E65A71F2B236B48C62CC /* Flutter.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = Flutter.release.xcconfig; sourceTree = "<group>"; };
		AC8FE152530FBDCE460186B326C1A8FB /* Pods-RunnerTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-RunnerTests.release.xcconfig"; sourceTree = "<group>"; };
		AE157A33FEF959A214796BFF348717F6 /* path_provider_foundation */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = path_provider_foundation; path = path_provider_foundation.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		AFCD559CA1BA984ED5BA9B329B9903E5 /* FLTIntegrationTestRunner.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = FLTIntegrationTestRunner.m; path = ../../../../../../../../../../../../../../opt/homebrew/Caskroom/flutter/3.32.5/flutter/packages/integration_test/ios/integration_test/Sources/integration_test/FLTIntegrationTestRunner.m; sourceTree = "<group>"; };
		B00A5ECAED4CA0C16FF7C65365BC0334 /* Pods-RunnerTests-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-RunnerTests-Info.plist"; sourceTree = "<group>"; };
		B08A01A7C65BD083B9CB27CFEE30EAF6 /* path_provider_foundation-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "path_provider_foundation-umbrella.h"; sourceTree = "<group>"; };
		B0BD57AB3672E53828D11C2A3368023A /* Pods-RunnerTests.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-RunnerTests.modulemap"; sourceTree = "<group>"; };
		B4B5755748E2628F111C9144B8405D63 /* integration_test-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "integration_test-umbrella.h"; sourceTree = "<group>"; };
		B6A123AA1A9854DFFC03245B97D03386 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/Resources/PrivacyInfo.xcprivacy"; sourceTree = "<group>"; };
		B703785507B22BAF0B800737429577EC /* Pods-Runner.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-Runner.release.xcconfig"; sourceTree = "<group>"; };
		BAC33B9913C38E9776028FE57E924002 /* path_provider_foundation-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "path_provider_foundation-dummy.m"; sourceTree = "<group>"; };
		BAD8FB194EB9EE34AFF29EC974273E7A /* path_provider_foundation.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = path_provider_foundation.podspec; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		BBBD982A8895B9EAF796285AD2663BAB /* path_provider_foundation-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "path_provider_foundation-Info.plist"; sourceTree = "<group>"; };
		BECFB4A3DD96CC16351381C773A62096 /* integration_test.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = integration_test.modulemap; sourceTree = "<group>"; };
		BF98CE53AA0A9F98E9E3CE4381487A0E /* flutter_secure_storage-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "flutter_secure_storage-umbrella.h"; sourceTree = "<group>"; };
		C1C1AD8C20613ACC67F00155D7BB54AA /* flutter_secure_storage */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = flutter_secure_storage; path = flutter_secure_storage.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		C24C1A035F22D24883C6F1876F274C6F /* Pods-Runner.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-Runner.modulemap"; sourceTree = "<group>"; };
		C50FD10CD19FF6999637BA5ECAF1681B /* Pods-Runner.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-Runner.profile.xcconfig"; sourceTree = "<group>"; };
		C832B181401ECD71E8170BF5694D9EF4 /* Flutter.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = Flutter.debug.xcconfig; sourceTree = "<group>"; };
		CC0D94152350147A46578098CD3319F0 /* integration_test.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = integration_test.podspec; path = ../../../../../../../../../../../opt/homebrew/Caskroom/flutter/3.32.5/flutter/packages/integration_test/ios/integration_test.podspec; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		D06E382C6CDB96104070CE3D98EA7F87 /* shared_preferences_foundation-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "shared_preferences_foundation-umbrella.h"; sourceTree = "<group>"; };
		D50B7242D2425C7E993B4D44DB1CEDCE /* FLTIntegrationTestRunner.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FLTIntegrationTestRunner.h; path = ../../../../../../../../../../../../../../../opt/homebrew/Caskroom/flutter/3.32.5/flutter/packages/integration_test/ios/integration_test/Sources/integration_test/include/FLTIntegrationTestRunner.h; sourceTree = "<group>"; };
		D6E248DF5C2842C7779A52949C00917F /* SwiftFlutterSecureStoragePlugin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SwiftFlutterSecureStoragePlugin.swift; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/ios/Classes/SwiftFlutterSecureStoragePlugin.swift"; sourceTree = "<group>"; };
		D8614E246D33BE30FF786DE1AA743E95 /* messages.g.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = messages.g.swift; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/messages.g.swift"; sourceTree = "<group>"; };
		DA7093F02EA2F146C1C8AEE7472CEBE9 /* IntegrationTestIosTest.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = IntegrationTestIosTest.m; path = ../../../../../../../../../../../../../../opt/homebrew/Caskroom/flutter/3.32.5/flutter/packages/integration_test/ios/integration_test/Sources/integration_test/IntegrationTestIosTest.m; sourceTree = "<group>"; };
		DC99D526361AF7877DBFBA9EFDD3A8D9 /* Flutter.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; path = Flutter.podspec; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		E03F62DA0DAC1A423FAC06435FBBAF62 /* FlutterSecureStoragePlugin.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = FlutterSecureStoragePlugin.h; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/ios/Classes/FlutterSecureStoragePlugin.h"; sourceTree = "<group>"; };
		E3C330B5F4227ED6FDC9FC042B5115D5 /* flutter_secure_storage-flutter_secure_storage */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "flutter_secure_storage-flutter_secure_storage"; path = flutter_secure_storage.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		E5AC58024FBF51814FC0BA9322824661 /* path_provider_foundation.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = path_provider_foundation.debug.xcconfig; sourceTree = "<group>"; };
		E5E68FAD1CFF59F065FF7C83FBF37E20 /* path_provider_foundation.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = path_provider_foundation.modulemap; sourceTree = "<group>"; };
		E736E0C9C5B73D27B8865E363E2108AC /* Pods-RunnerTests.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-RunnerTests.profile.xcconfig"; sourceTree = "<group>"; };
		E794494AB90477BA94C681E994ADF341 /* Pods-Runner-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-Runner-dummy.m"; sourceTree = "<group>"; };
		E84E5F16011D652C38360CB7E4D8D0A4 /* ResourceBundle-flutter_secure_storage-flutter_secure_storage-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-flutter_secure_storage-flutter_secure_storage-Info.plist"; sourceTree = "<group>"; };
		ECDF6AA713870989438BA93B395DC557 /* Pods-Runner-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-Runner-acknowledgements.markdown"; sourceTree = "<group>"; };
		ECF4CA3AF5536D14B4E015504E7AEEC0 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/UIKit.framework; sourceTree = DEVELOPER_DIR; };
		F170D551E7111567220ED44EF78572C7 /* FlutterSecureStorage.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FlutterSecureStorage.swift; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/ios/Classes/FlutterSecureStorage.swift"; sourceTree = "<group>"; };
		F6C83932A215118C36531D3F1FC11D26 /* flutter_secure_storage-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "flutter_secure_storage-Info.plist"; sourceTree = "<group>"; };
		F826B5A62132A1021AC1C0006243A58B /* shared_preferences_foundation.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = shared_preferences_foundation.release.xcconfig; sourceTree = "<group>"; };
		F93E743BA51940AB50FCDDEA920E31F8 /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE"; sourceTree = "<group>"; };
		FC8B4582EA4ADB2E92011673BA7B2B1C /* messages.g.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = messages.g.swift; path = "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/messages.g.swift"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		2C51CA6EC745ACB63E424757C710B36C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				E8B800C8DB1A0A612E05CE6661CCA6A9 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4380924F566AA01EB048DC15F9BC6D33 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				CEBD84922D2CCEF26C272418EC3EB3A6 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		55965BBBD5D76CA4DB7DFBB01AA39E58 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				898AE8C3E44E2345DAEAD5B11FEB1A49 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		847096248A5142E132437E75305D0198 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				C0B8533CF680A699DFAC3233AA9FA22C /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B6E4FEEAF8CA9AEFFE9D7F258386DB89 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BFF5908BE98D5049B50C642FBF3FA67C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C60368AE5AB595A6FB47AB86BB81F10B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				E6B934071CC8AAC4C2F13418E980DC83 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C94696043CAD87B6B699BAB367B53A54 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D37529713F6219C15572CE565F23780E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				898F57570FA3E297EA6915712A16B230 /* Foundation.framework in Frameworks */,
				CE777A3D5F9030D78EBCEA607A828715 /* UIKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		08986DC03FEACACF42CE50159EEC93DA /* shared_preferences_foundation */ = {
			isa = PBXGroup;
			children = (
				F31E127AE360C5A016375B1359233246 /* Sources */,
			);
			name = shared_preferences_foundation;
			path = shared_preferences_foundation;
			sourceTree = "<group>";
		};
		0C8E1D40392BE0CA8BED8CB497E055EC /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				295ED5F3772F242FF30B60D3B6FD7DBC /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		0EB6AEB171A3C385B2A83810D402EFF3 /* .. */ = {
			isa = PBXGroup;
			children = (
				D95A2A61B7891D68C9AFF3C5F757D73F /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		0EC3CB2C0308A7EF6995EA1D392B9126 /* Flutter */ = {
			isa = PBXGroup;
			children = (
				81628D6990DB7F28A0925C54ACF47F94 /* Pod */,
				A365794E741BD94DA1C8FA45338F4A5A /* Support Files */,
			);
			name = Flutter;
			path = ../Flutter;
			sourceTree = "<group>";
		};
		1141DE25B7BC1C05302E4C6FB111E0B0 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				28EE18573825B4A3159EF5A7B502A647 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		142C1C0F1BAE16103C189A27816B307A /* darwin */ = {
			isa = PBXGroup;
			children = (
				E7B799A1AAB6899269D5B88735E6FF40 /* path_provider_foundation */,
			);
			name = darwin;
			path = darwin;
			sourceTree = "<group>";
		};
		1540493FB4D7BBB34AB78ADE3D7F9A5F /* flutter_secure_storage */ = {
			isa = PBXGroup;
			children = (
				5677A14AE2A54C2C88B8646D24A5E556 /* ios */,
			);
			name = flutter_secure_storage;
			path = flutter_secure_storage;
			sourceTree = "<group>";
		};
		1628BF05B4CAFDCC3549A101F5A10A17 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				1DE6440B6853131A80C53DDA34375AAB /* iOS */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		1DBC87F2725BCA3B67836AC7E938216E /* include */ = {
			isa = PBXGroup;
			children = (
				D50B7242D2425C7E993B4D44DB1CEDCE /* FLTIntegrationTestRunner.h */,
				85FB06C973B6B0C00D6C2741371B3644 /* IntegrationTestIosTest.h */,
				3B138BC214B1B8A5C0766D34B55F6E6D /* IntegrationTestPlugin.h */,
			);
			name = include;
			path = include;
			sourceTree = "<group>";
		};
		1DE6440B6853131A80C53DDA34375AAB /* iOS */ = {
			isa = PBXGroup;
			children = (
				8193349D819536E58C58A34C1B7DF545 /* Foundation.framework */,
				ECF4CA3AF5536D14B4E015504E7AEEC0 /* UIKit.framework */,
			);
			name = iOS;
			sourceTree = "<group>";
		};
		1E48898A8BBF8404DF57A0B6F1E0B0FB /* Sources */ = {
			isa = PBXGroup;
			children = (
				83F9D8209858A81342502F27EA16662F /* integration_test */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		1E71AB2ECAED3491F7CBE5B1AF0C7BCC /* news_collector_app */ = {
			isa = PBXGroup;
			children = (
				220D30ABF77C905A4F343CEB7D05B43E /* ios */,
			);
			name = news_collector_app;
			path = news_collector_app;
			sourceTree = "<group>";
		};
		206EE0DFACFABC6BBEA3222580A02FA0 /* flutter_secure_storage */ = {
			isa = PBXGroup;
			children = (
				2F6EB43B3A90DA9D39C4118954252700 /* .. */,
				F88929676774334AD3ECF3B7551C69FF /* Pod */,
				DA743813AF90E423997F2AE81333F119 /* Support Files */,
			);
			name = flutter_secure_storage;
			path = ../.symlinks/plugins/flutter_secure_storage/ios;
			sourceTree = "<group>";
		};
		220D30ABF77C905A4F343CEB7D05B43E /* ios */ = {
			isa = PBXGroup;
			children = (
				9D11E30616B9601DB5FDCA97F7C67ED3 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		279C2F6321113C7B9A2B1DE1C3907E15 /* shared_preferences_foundation */ = {
			isa = PBXGroup;
			children = (
				79DF80EC9C26C0D92CB36CEC1D4DF5AB /* Sources */,
			);
			name = shared_preferences_foundation;
			path = shared_preferences_foundation;
			sourceTree = "<group>";
		};
		28EE18573825B4A3159EF5A7B502A647 /* plugins */ = {
			isa = PBXGroup;
			children = (
				445097ADE83532FBE2CC223687ED7F83 /* path_provider_foundation */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		295ED5F3772F242FF30B60D3B6FD7DBC /* plugins */ = {
			isa = PBXGroup;
			children = (
				A9019B5919C45448C09013426C391222 /* shared_preferences_foundation */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		2A29559B55DB44C6CA782BB789BA6DD9 /* ios */ = {
			isa = PBXGroup;
			children = (
				5A196581D983B60FF6E81339216E9048 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		2A5228A113D942EC91DD2E09480EECE3 /* VS code file */ = {
			isa = PBXGroup;
			children = (
				919E1112EE6313EC9EDDA431E1467953 /* News_collector_UI */,
			);
			name = "VS code file";
			path = "VS code file";
			sourceTree = "<group>";
		};
		2D541B6DA2E3E8467AB8781A727BB8A3 /* .. */ = {
			isa = PBXGroup;
			children = (
				6FF3B05E00E01E99F13411E09C53D316 /* .. */,
				E08BBDDC757689EB956E86ECE9BF7D53 /* Desktop */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		2DB8426A477BA92773F00012EBA84D53 /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				E806A4F97D48F5FD3B4CA1BC617EA1F5 /* Pods-Runner */,
				537E840C4F4E30B706FD9D64B15C53A7 /* Pods-RunnerTests */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		2F6EB43B3A90DA9D39C4118954252700 /* .. */ = {
			isa = PBXGroup;
			children = (
				0EB6AEB171A3C385B2A83810D402EFF3 /* .. */,
			);
			name = ..;
			path = "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/ios";
			sourceTree = "<group>";
		};
		3067419AF6E6E4E4C26A8CEAE52CBA62 /* .. */ = {
			isa = PBXGroup;
			children = (
				D70A40F676368294E30A9BE32EA5883B /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		30C7AFCBA6FEDFCFAC1D59E18D62EFDA /* ios */ = {
			isa = PBXGroup;
			children = (
				9297AF466F51B6A313903B6748D99C87 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		324C94764AC1E54C790FA33F0A9AD811 /* darwin */ = {
			isa = PBXGroup;
			children = (
				443B6C72763674DF3D8F7EE5A31FC1C3 /* path_provider_foundation */,
			);
			name = darwin;
			path = darwin;
			sourceTree = "<group>";
		};
		326552A01DAB3E67B88DC2B16A0C204D /* news_collector_app */ = {
			isa = PBXGroup;
			children = (
				DE14349067A22636A6D93AD6F676CBD2 /* ios */,
			);
			name = news_collector_app;
			path = news_collector_app;
			sourceTree = "<group>";
		};
		32EDB52DDDD4DBFCB82D9C2EF0AD2E6F /* Support Files */ = {
			isa = PBXGroup;
			children = (
				E5E68FAD1CFF59F065FF7C83FBF37E20 /* path_provider_foundation.modulemap */,
				BAC33B9913C38E9776028FE57E924002 /* path_provider_foundation-dummy.m */,
				BBBD982A8895B9EAF796285AD2663BAB /* path_provider_foundation-Info.plist */,
				7CD70C2DFF395D68D3A6C9DCE0E38D80 /* path_provider_foundation-prefix.pch */,
				B08A01A7C65BD083B9CB27CFEE30EAF6 /* path_provider_foundation-umbrella.h */,
				E5AC58024FBF51814FC0BA9322824661 /* path_provider_foundation.debug.xcconfig */,
				3C29A9A0523675E67F6E9B274BF36B06 /* path_provider_foundation.release.xcconfig */,
				27BB2B51652CEC054A67F299BA15E067 /* ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist */,
			);
			name = "Support Files";
			path = "../../../../Pods/Target Support Files/path_provider_foundation";
			sourceTree = "<group>";
		};
		362BFC8CF8D6B0F7E08F1BFB684D0037 /* VS code file */ = {
			isa = PBXGroup;
			children = (
				9FDEF7BB32347E8A3DE15018F0E1A15E /* News_collector_UI */,
			);
			name = "VS code file";
			path = "VS code file";
			sourceTree = "<group>";
		};
		38C2232F8FE76C7FD2D4D3EFEA975990 /* plugins */ = {
			isa = PBXGroup;
			children = (
				D8964098545F4233D15A50D36E872A9D /* path_provider_foundation */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		390D22D6B689C97B09523EAD0ECE1F5F /* Pod */ = {
			isa = PBXGroup;
			children = (
				F93E743BA51940AB50FCDDEA920E31F8 /* LICENSE */,
				1E3F89FC19634A91DE060C1CEE7E0D36 /* shared_preferences_foundation.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		3BAB18968B0C72E479E8403BC1260B7B /* integration_test */ = {
			isa = PBXGroup;
			children = (
				9D1494FCA2354D0E9604E5CD78876D8E /* ios */,
			);
			name = integration_test;
			path = integration_test;
			sourceTree = "<group>";
		};
		42256212D3FDDA2B81C5AE7693BE318E /* darwin */ = {
			isa = PBXGroup;
			children = (
				08986DC03FEACACF42CE50159EEC93DA /* shared_preferences_foundation */,
			);
			name = darwin;
			path = darwin;
			sourceTree = "<group>";
		};
		44393F10A79E8F6F11BB58154EBAABF2 /* darwin */ = {
			isa = PBXGroup;
			children = (
				279C2F6321113C7B9A2B1DE1C3907E15 /* shared_preferences_foundation */,
			);
			name = darwin;
			path = darwin;
			sourceTree = "<group>";
		};
		443B6C72763674DF3D8F7EE5A31FC1C3 /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				8042189977D28BA3CA09B093F4AC4592 /* Sources */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		445097ADE83532FBE2CC223687ED7F83 /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				142C1C0F1BAE16103C189A27816B307A /* darwin */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		44853D39182A5ABA2B2BF805F5AC87AD /* news_collector_app */ = {
			isa = PBXGroup;
			children = (
				2A29559B55DB44C6CA782BB789BA6DD9 /* ios */,
			);
			name = news_collector_app;
			path = news_collector_app;
			sourceTree = "<group>";
		};
		4577D9C5B02D6730971FB2FE624BE3E3 /* .. */ = {
			isa = PBXGroup;
			children = (
				C6F34E0DAD24638255993F06451863A7 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		4819D6306B96492C99E079395891966B /* plugins */ = {
			isa = PBXGroup;
			children = (
				C14A25333432FC4FDBE0068A481BC8FC /* shared_preferences_foundation */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		4AD764B1FD7A22E74A6D28B30224F69E /* Products */ = {
			isa = PBXGroup;
			children = (
				C1C1AD8C20613ACC67F00155D7BB54AA /* flutter_secure_storage */,
				E3C330B5F4227ED6FDC9FC042B5115D5 /* flutter_secure_storage-flutter_secure_storage */,
				5B707EA37CBC3DFDABC9D9DFAD54F4BD /* integration_test */,
				AE157A33FEF959A214796BFF348717F6 /* path_provider_foundation */,
				3DBD4BADE27F8B91024E4B4B4DD75DB5 /* path_provider_foundation-path_provider_foundation_privacy */,
				669E8F25E1897672BDB80B7EB784DA24 /* Pods-Runner */,
				6C3345B1B3CAEDF5B03B1F731FDC492E /* Pods-RunnerTests */,
				93C6A45C6E4792269BE9BE0073839BF0 /* shared_preferences_foundation */,
				0652FCE3FC19056983AABE058B3CC45B /* shared_preferences_foundation-shared_preferences_foundation_privacy */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		537E840C4F4E30B706FD9D64B15C53A7 /* Pods-RunnerTests */ = {
			isa = PBXGroup;
			children = (
				B0BD57AB3672E53828D11C2A3368023A /* Pods-RunnerTests.modulemap */,
				11AA7018F95317D959D9D2E76CBB2377 /* Pods-RunnerTests-acknowledgements.markdown */,
				79F83F4D505FA40C1CD18A8029181D59 /* Pods-RunnerTests-acknowledgements.plist */,
				4D3DB54C128E3D88C0C9C7DDEA475ED7 /* Pods-RunnerTests-dummy.m */,
				B00A5ECAED4CA0C16FF7C65365BC0334 /* Pods-RunnerTests-Info.plist */,
				9E9B12BCE5540D02A80250C3EC16D399 /* Pods-RunnerTests-umbrella.h */,
				03E8AD2E8B75443E12D3D3BD12A9900E /* Pods-RunnerTests.debug.xcconfig */,
				E736E0C9C5B73D27B8865E363E2108AC /* Pods-RunnerTests.profile.xcconfig */,
				AC8FE152530FBDCE460186B326C1A8FB /* Pods-RunnerTests.release.xcconfig */,
			);
			name = "Pods-RunnerTests";
			path = "Target Support Files/Pods-RunnerTests";
			sourceTree = "<group>";
		};
		5677A14AE2A54C2C88B8646D24A5E556 /* ios */ = {
			isa = PBXGroup;
			children = (
				EB08B4BF6981122717FB8D0F7514D623 /* Classes */,
				D0962A19007F157F2B0ABE188390F1AC /* Resources */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		595D6E6771481267DA691E457ED132D7 /* .. */ = {
			isa = PBXGroup;
			children = (
				3067419AF6E6E4E4C26A8CEAE52CBA62 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		599CD4443EB990B1E5D8B6BC959E6E6D /* .. */ = {
			isa = PBXGroup;
			children = (
				CEA294C9BEA38C8EA1C5B55EB1AF3D5A /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		59D02CA241B7FF9002C0DC22C46C5466 /* Desktop */ = {
			isa = PBXGroup;
			children = (
				72A57F67B7E3A5DD4E8027EF08DAE2F8 /* VS code file */,
			);
			name = Desktop;
			path = Desktop;
			sourceTree = "<group>";
		};
		5A196581D983B60FF6E81339216E9048 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				4819D6306B96492C99E079395891966B /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		5AD19A818593A80431C22A8DD96AF814 /* Users */ = {
			isa = PBXGroup;
			children = (
				8A410E6BB9FD646D845E23FF99B3B3EF /* howard */,
			);
			name = Users;
			path = Users;
			sourceTree = "<group>";
		};
		5D575BC60D412FCD7B83AE78C3109E4A /* VS code file */ = {
			isa = PBXGroup;
			children = (
				A4B45FD51F5F51C1E2A09FCDCA4534C9 /* News_collector_UI */,
			);
			name = "VS code file";
			path = "VS code file";
			sourceTree = "<group>";
		};
		6872BEEF07065E6D39786164DA2727E0 /* Resources */ = {
			isa = PBXGroup;
			children = (
				5809C5699B36C6515BA24827691DD4F3 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			path = Resources;
			sourceTree = "<group>";
		};
		699079E047CDAF3C4A45E60F7EFF751D /* .. */ = {
			isa = PBXGroup;
			children = (
				AE9D411B31FAB43C76DB1B8569FECDFB /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		6A660E791995A88848494875D48BC53A /* .. */ = {
			isa = PBXGroup;
			children = (
				7F1B5C9C0082285B181279FFD9A18785 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		6B77A708D3B0AE8E3852EC9AB18BA6FE /* VS code file */ = {
			isa = PBXGroup;
			children = (
				76663B87F71F80C2F249773524FD7915 /* News_collector_UI */,
			);
			name = "VS code file";
			path = "VS code file";
			sourceTree = "<group>";
		};
		6C2A87A61646CC99C5DFB354FCBC30F4 /* .. */ = {
			isa = PBXGroup;
			children = (
				6D319AC0D75187F5FF92D2266B8904E8 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		6D319AC0D75187F5FF92D2266B8904E8 /* .. */ = {
			isa = PBXGroup;
			children = (
				7874E4842E9E13A677993E57974E4735 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		6FF3B05E00E01E99F13411E09C53D316 /* .. */ = {
			isa = PBXGroup;
			children = (
				8E483ED34D14AEAB72A2ADC122EAC57A /* Desktop */,
			);
			name = ..;
			path = .;
			sourceTree = "<group>";
		};
		72A57F67B7E3A5DD4E8027EF08DAE2F8 /* VS code file */ = {
			isa = PBXGroup;
			children = (
				896AEA5C44BE5F5747A3726B3E70D7FA /* News_collector_UI */,
			);
			name = "VS code file";
			path = "VS code file";
			sourceTree = "<group>";
		};
		72D63621673A183F1F4CECD254221C5B /* .. */ = {
			isa = PBXGroup;
			children = (
				9FE04D1B3370C018D9B8762FE45AE2E9 /* .. */,
			);
			name = ..;
			path = ../../../../../../../../../../../opt/homebrew/Caskroom/flutter/3.32.5/flutter/packages/integration_test/ios/integration_test/Sources;
			sourceTree = "<group>";
		};
		7454FE3E037A917A16DA64C28E53AF77 /* Sources */ = {
			isa = PBXGroup;
			children = (
				B4C8E6B1DF83B9C464F0FE379BAE9F76 /* path_provider_foundation */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		76663B87F71F80C2F249773524FD7915 /* News_collector_UI */ = {
			isa = PBXGroup;
			children = (
				1E71AB2ECAED3491F7CBE5B1AF0C7BCC /* news_collector_app */,
			);
			name = News_collector_UI;
			path = News_collector_UI;
			sourceTree = "<group>";
		};
		7874E4842E9E13A677993E57974E4735 /* .. */ = {
			isa = PBXGroup;
			children = (
				E205FA2F006F841A9964358B25EDB33A /* Desktop */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		79DF80EC9C26C0D92CB36CEC1D4DF5AB /* Sources */ = {
			isa = PBXGroup;
			children = (
				9E00845E6C1D9520670BF3BDB2720091 /* shared_preferences_foundation */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		7B325632DCFEC9718752BB33E4EF2279 /* News_collector_UI */ = {
			isa = PBXGroup;
			children = (
				BF8199C8D1680A20EB379FE80A6F71FB /* news_collector_app */,
			);
			name = News_collector_UI;
			path = News_collector_UI;
			sourceTree = "<group>";
		};
		7BFBD40B1D4426167FA096320F11B2AB /* Pod */ = {
			isa = PBXGroup;
			children = (
				7CF8FE195D467394157BBE62DDB462A0 /* LICENSE */,
				BAD8FB194EB9EE34AFF29EC974273E7A /* path_provider_foundation.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		7DA26D9ED9518CD16484E4D3288AA131 /* .. */ = {
			isa = PBXGroup;
			children = (
				6A660E791995A88848494875D48BC53A /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		7DBD56006889F5E1CDB6B05ACDCFAEA7 /* ios */ = {
			isa = PBXGroup;
			children = (
				8EE013A971526E33F0D2EC742B204DD9 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		7E4C033F5E4FB3B42543F7D6967E7CFB /* .. */ = {
			isa = PBXGroup;
			children = (
				E87689233F5DD20668961830E2507D47 /* Desktop */,
			);
			name = ..;
			path = .;
			sourceTree = "<group>";
		};
		7F1B5C9C0082285B181279FFD9A18785 /* .. */ = {
			isa = PBXGroup;
			children = (
				599CD4443EB990B1E5D8B6BC959E6E6D /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		8042189977D28BA3CA09B093F4AC4592 /* Sources */ = {
			isa = PBXGroup;
			children = (
				A1AB8D141885A75ACB5B8083F0946F18 /* path_provider_foundation */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		81628D6990DB7F28A0925C54ACF47F94 /* Pod */ = {
			isa = PBXGroup;
			children = (
				DC99D526361AF7877DBFBA9EFDD3A8D9 /* Flutter.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		81EB6A6C10DA19FABB2A743CFD265483 /* .. */ = {
			isa = PBXGroup;
			children = (
				F5E666977DBFED03BECBA39009CDCAA2 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		830F5D8D612B7F6C4B4038568E5CB266 /* .. */ = {
			isa = PBXGroup;
			children = (
				8A89E0499FD412EA0D2CF961539489DE /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		83F9D8209858A81342502F27EA16662F /* integration_test */ = {
			isa = PBXGroup;
			children = (
				AFCD559CA1BA984ED5BA9B329B9903E5 /* FLTIntegrationTestRunner.m */,
				DA7093F02EA2F146C1C8AEE7472CEBE9 /* IntegrationTestIosTest.m */,
				888697E17222CBD04C018B67CB69E7EF /* IntegrationTestPlugin.m */,
				1DBC87F2725BCA3B67836AC7E938216E /* include */,
			);
			name = integration_test;
			path = integration_test;
			sourceTree = "<group>";
		};
		8538A88B508B910CCADF655F9CF05C21 /* .. */ = {
			isa = PBXGroup;
			children = (
				F0BCE5D2279C3A0D0F059BBF7EF920E3 /* .. */,
			);
			name = ..;
			path = "../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources";
			sourceTree = "<group>";
		};
		896AEA5C44BE5F5747A3726B3E70D7FA /* News_collector_UI */ = {
			isa = PBXGroup;
			children = (
				F49966BFC72DC995B82FCEF77F6DA52B /* news_collector_app */,
			);
			name = News_collector_UI;
			path = News_collector_UI;
			sourceTree = "<group>";
		};
		8A410E6BB9FD646D845E23FF99B3B3EF /* howard */ = {
			isa = PBXGroup;
			children = (
				59D02CA241B7FF9002C0DC22C46C5466 /* Desktop */,
			);
			name = howard;
			path = howard;
			sourceTree = "<group>";
		};
		8A89E0499FD412EA0D2CF961539489DE /* .. */ = {
			isa = PBXGroup;
			children = (
				C481A77817965AA9F4F024E508FEEF8A /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		8E483ED34D14AEAB72A2ADC122EAC57A /* Desktop */ = {
			isa = PBXGroup;
			children = (
				5D575BC60D412FCD7B83AE78C3109E4A /* VS code file */,
			);
			name = Desktop;
			path = Desktop;
			sourceTree = "<group>";
		};
		8EE013A971526E33F0D2EC742B204DD9 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				EA30E1898B8F82D74BC5FC63917025CD /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		919E1112EE6313EC9EDDA431E1467953 /* News_collector_UI */ = {
			isa = PBXGroup;
			children = (
				44853D39182A5ABA2B2BF805F5AC87AD /* news_collector_app */,
			);
			name = News_collector_UI;
			path = News_collector_UI;
			sourceTree = "<group>";
		};
		9297AF466F51B6A313903B6748D99C87 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				38C2232F8FE76C7FD2D4D3EFEA975990 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		995E85ADFD33BCF1AFB881B21A0A452E /* .. */ = {
			isa = PBXGroup;
			children = (
				7E4C033F5E4FB3B42543F7D6967E7CFB /* .. */,
				D2E4CA0A21C2743AB61007B22E1B51E9 /* Desktop */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		99C3A4A98022993F3D71563FAD436D8E /* ios */ = {
			isa = PBXGroup;
			children = (
				1141DE25B7BC1C05302E4C6FB111E0B0 /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		9B1A0F4A2CFF8AC423AF130FC935F0B1 /* .. */ = {
			isa = PBXGroup;
			children = (
				2D541B6DA2E3E8467AB8781A727BB8A3 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		9D11E30616B9601DB5FDCA97F7C67ED3 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				FA499B65D334C3D519E12323939DF37F /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		9D1494FCA2354D0E9604E5CD78876D8E /* ios */ = {
			isa = PBXGroup;
			children = (
				B21F034CD42C3AF2296A621B97F28304 /* integration_test */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		9E00845E6C1D9520670BF3BDB2720091 /* shared_preferences_foundation */ = {
			isa = PBXGroup;
			children = (
				F0A4E01A385C59DB542E92E35FBC7039 /* Resources */,
			);
			name = shared_preferences_foundation;
			path = shared_preferences_foundation;
			sourceTree = "<group>";
		};
		9FDEF7BB32347E8A3DE15018F0E1A15E /* News_collector_UI */ = {
			isa = PBXGroup;
			children = (
				B9DE3311F70AD2809AE5CB6CA43F6B08 /* news_collector_app */,
			);
			name = News_collector_UI;
			path = News_collector_UI;
			sourceTree = "<group>";
		};
		9FE04D1B3370C018D9B8762FE45AE2E9 /* .. */ = {
			isa = PBXGroup;
			children = (
				FD3951177BC983626917ED903890475F /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		A1AB8D141885A75ACB5B8083F0946F18 /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				FC8B4582EA4ADB2E92011673BA7B2B1C /* messages.g.swift */,
				53F5E3A35C16FDB0D3A9F2EADD6BE4A9 /* PathProviderPlugin.swift */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		A365794E741BD94DA1C8FA45338F4A5A /* Support Files */ = {
			isa = PBXGroup;
			children = (
				C832B181401ECD71E8170BF5694D9EF4 /* Flutter.debug.xcconfig */,
				AA6B2500D7C0E65A71F2B236B48C62CC /* Flutter.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Pods/Target Support Files/Flutter";
			sourceTree = "<group>";
		};
		A4B45FD51F5F51C1E2A09FCDCA4534C9 /* News_collector_UI */ = {
			isa = PBXGroup;
			children = (
				326552A01DAB3E67B88DC2B16A0C204D /* news_collector_app */,
			);
			name = News_collector_UI;
			path = News_collector_UI;
			sourceTree = "<group>";
		};
		A9019B5919C45448C09013426C391222 /* shared_preferences_foundation */ = {
			isa = PBXGroup;
			children = (
				44393F10A79E8F6F11BB58154EBAABF2 /* darwin */,
			);
			name = shared_preferences_foundation;
			path = shared_preferences_foundation;
			sourceTree = "<group>";
		};
		AE9D411B31FAB43C76DB1B8569FECDFB /* .. */ = {
			isa = PBXGroup;
			children = (
				4577D9C5B02D6730971FB2FE624BE3E3 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		B21F034CD42C3AF2296A621B97F28304 /* integration_test */ = {
			isa = PBXGroup;
			children = (
				1E48898A8BBF8404DF57A0B6F1E0B0FB /* Sources */,
			);
			name = integration_test;
			path = integration_test;
			sourceTree = "<group>";
		};
		B2A1DEAF91FA776C8E760124051326C7 /* integration_test */ = {
			isa = PBXGroup;
			children = (
				72D63621673A183F1F4CECD254221C5B /* .. */,
				D2BA2B7A2D67CDB326C24F65BE694B58 /* Pod */,
				BDC91C2B4F8B0C59F58C4A1A39262FA3 /* Support Files */,
			);
			name = integration_test;
			path = ../.symlinks/plugins/integration_test/ios;
			sourceTree = "<group>";
		};
		B4C8E6B1DF83B9C464F0FE379BAE9F76 /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				6872BEEF07065E6D39786164DA2727E0 /* Resources */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		B9DE3311F70AD2809AE5CB6CA43F6B08 /* news_collector_app */ = {
			isa = PBXGroup;
			children = (
				30C7AFCBA6FEDFCFAC1D59E18D62EFDA /* ios */,
			);
			name = news_collector_app;
			path = news_collector_app;
			sourceTree = "<group>";
		};
		BDC91C2B4F8B0C59F58C4A1A39262FA3 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				BECFB4A3DD96CC16351381C773A62096 /* integration_test.modulemap */,
				71133531204ABAC4D3DAEA3CB35E51E2 /* integration_test-dummy.m */,
				912C49F87F0197643EA8696407DB04AB /* integration_test-Info.plist */,
				6B8FAA5A1A40A9009E9E7F7289903DC4 /* integration_test-prefix.pch */,
				B4B5755748E2628F111C9144B8405D63 /* integration_test-umbrella.h */,
				410328C0734E96B2D80C03B3D59A6F59 /* integration_test.debug.xcconfig */,
				34B0A9C091ED932F04FD1D3AF160CEB4 /* integration_test.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../../../Pods/Target Support Files/integration_test";
			sourceTree = "<group>";
		};
		BF8199C8D1680A20EB379FE80A6F71FB /* news_collector_app */ = {
			isa = PBXGroup;
			children = (
				99C3A4A98022993F3D71563FAD436D8E /* ios */,
			);
			name = news_collector_app;
			path = news_collector_app;
			sourceTree = "<group>";
		};
		C14A25333432FC4FDBE0068A481BC8FC /* shared_preferences_foundation */ = {
			isa = PBXGroup;
			children = (
				42256212D3FDDA2B81C5AE7693BE318E /* darwin */,
			);
			name = shared_preferences_foundation;
			path = shared_preferences_foundation;
			sourceTree = "<group>";
		};
		C481A77817965AA9F4F024E508FEEF8A /* .. */ = {
			isa = PBXGroup;
			children = (
				9B1A0F4A2CFF8AC423AF130FC935F0B1 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		C6F34E0DAD24638255993F06451863A7 /* .. */ = {
			isa = PBXGroup;
			children = (
				7DA26D9ED9518CD16484E4D3288AA131 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		C6F5C02C7B6AD0C44A1DAF36252613D2 /* .. */ = {
			isa = PBXGroup;
			children = (
				81EB6A6C10DA19FABB2A743CFD265483 /* .. */,
			);
			name = ..;
			path = "../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources";
			sourceTree = "<group>";
		};
		CADEACE6277C8451424869980BAC80A7 /* shared_preferences_foundation */ = {
			isa = PBXGroup;
			children = (
				D8614E246D33BE30FF786DE1AA743E95 /* messages.g.swift */,
				869490557E12485F15232D747E2B917B /* SharedPreferencesPlugin.swift */,
			);
			name = shared_preferences_foundation;
			path = shared_preferences_foundation;
			sourceTree = "<group>";
		};
		CEA294C9BEA38C8EA1C5B55EB1AF3D5A /* .. */ = {
			isa = PBXGroup;
			children = (
				5AD19A818593A80431C22A8DD96AF814 /* Users */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		CF1408CF629C7361332E53B88F7BD30C = {
			isa = PBXGroup;
			children = (
				9D940727FF8FB9C785EB98E56350EF41 /* Podfile */,
				D1D6CE24B23A0334981A3BD520136946 /* Development Pods */,
				1628BF05B4CAFDCC3549A101F5A10A17 /* Frameworks */,
				4AD764B1FD7A22E74A6D28B30224F69E /* Products */,
				2DB8426A477BA92773F00012EBA84D53 /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		D0962A19007F157F2B0ABE188390F1AC /* Resources */ = {
			isa = PBXGroup;
			children = (
				485F2108AD79BE45C0D7EB400113BB73 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			path = Resources;
			sourceTree = "<group>";
		};
		D1D6CE24B23A0334981A3BD520136946 /* Development Pods */ = {
			isa = PBXGroup;
			children = (
				0EC3CB2C0308A7EF6995EA1D392B9126 /* Flutter */,
				206EE0DFACFABC6BBEA3222580A02FA0 /* flutter_secure_storage */,
				B2A1DEAF91FA776C8E760124051326C7 /* integration_test */,
				D7FA0B0A4E5FFEF2FEB16E250B64B4D6 /* path_provider_foundation */,
				FFFD9450D605A8DB42D836C6FAEA6B86 /* shared_preferences_foundation */,
			);
			name = "Development Pods";
			sourceTree = "<group>";
		};
		D2BA2B7A2D67CDB326C24F65BE694B58 /* Pod */ = {
			isa = PBXGroup;
			children = (
				CC0D94152350147A46578098CD3319F0 /* integration_test.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		D2E4CA0A21C2743AB61007B22E1B51E9 /* Desktop */ = {
			isa = PBXGroup;
			children = (
				362BFC8CF8D6B0F7E08F1BFB684D0037 /* VS code file */,
			);
			name = Desktop;
			path = Desktop;
			sourceTree = "<group>";
		};
		D6E9282D91833F2CD81CB5FF3A2AF42A /* .. */ = {
			isa = PBXGroup;
			children = (
				595D6E6771481267DA691E457ED132D7 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		D70A40F676368294E30A9BE32EA5883B /* .. */ = {
			isa = PBXGroup;
			children = (
				E4E29E5761E15A93CAF047FBFA7909E6 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		D7FA0B0A4E5FFEF2FEB16E250B64B4D6 /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				8538A88B508B910CCADF655F9CF05C21 /* .. */,
				7BFBD40B1D4426167FA096320F11B2AB /* Pod */,
				32EDB52DDDD4DBFCB82D9C2EF0AD2E6F /* Support Files */,
			);
			name = path_provider_foundation;
			path = ../.symlinks/plugins/path_provider_foundation/darwin;
			sourceTree = "<group>";
		};
		D8964098545F4233D15A50D36E872A9D /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				324C94764AC1E54C790FA33F0A9AD811 /* darwin */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		D95A2A61B7891D68C9AFF3C5F757D73F /* .. */ = {
			isa = PBXGroup;
			children = (
				6C2A87A61646CC99C5DFB354FCBC30F4 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		DA743813AF90E423997F2AE81333F119 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				415DF6EE06CCCB12BEDC901928FF5BEC /* flutter_secure_storage.modulemap */,
				356005FC6DE78797A2695EE823F9C11F /* flutter_secure_storage-dummy.m */,
				F6C83932A215118C36531D3F1FC11D26 /* flutter_secure_storage-Info.plist */,
				8B261F0229DB316C6D31B1254C453D70 /* flutter_secure_storage-prefix.pch */,
				BF98CE53AA0A9F98E9E3CE4381487A0E /* flutter_secure_storage-umbrella.h */,
				0A75BD300C59519EA3ABFB5D75CCDC0D /* flutter_secure_storage.debug.xcconfig */,
				A63E04ED2197F574A1C39A8AC54DC8B5 /* flutter_secure_storage.release.xcconfig */,
				E84E5F16011D652C38360CB7E4D8D0A4 /* ResourceBundle-flutter_secure_storage-flutter_secure_storage-Info.plist */,
			);
			name = "Support Files";
			path = "../../../../Pods/Target Support Files/flutter_secure_storage";
			sourceTree = "<group>";
		};
		DC9653E4AD344C1C5C39FF671E43B128 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				3E8A27C782E727490CC28DF6C23CB746 /* ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist */,
				1B6E7B07080106A083908D87BF2327F2 /* shared_preferences_foundation.modulemap */,
				873F0477514EDF6C041911D3714FFA11 /* shared_preferences_foundation-dummy.m */,
				130B3DF2AC68DA4C75EF43E0CD0946DA /* shared_preferences_foundation-Info.plist */,
				024D2185AA87C3EA58BD549083AD3502 /* shared_preferences_foundation-prefix.pch */,
				D06E382C6CDB96104070CE3D98EA7F87 /* shared_preferences_foundation-umbrella.h */,
				5EF8C76469D7ACFC619D6C69A7C2A8E7 /* shared_preferences_foundation.debug.xcconfig */,
				F826B5A62132A1021AC1C0006243A58B /* shared_preferences_foundation.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../../../Pods/Target Support Files/shared_preferences_foundation";
			sourceTree = "<group>";
		};
		DE14349067A22636A6D93AD6F676CBD2 /* ios */ = {
			isa = PBXGroup;
			children = (
				0C8E1D40392BE0CA8BED8CB497E055EC /* .symlinks */,
			);
			name = ios;
			path = ios;
			sourceTree = "<group>";
		};
		E08BBDDC757689EB956E86ECE9BF7D53 /* Desktop */ = {
			isa = PBXGroup;
			children = (
				2A5228A113D942EC91DD2E09480EECE3 /* VS code file */,
			);
			name = Desktop;
			path = Desktop;
			sourceTree = "<group>";
		};
		E205FA2F006F841A9964358B25EDB33A /* Desktop */ = {
			isa = PBXGroup;
			children = (
				6B77A708D3B0AE8E3852EC9AB18BA6FE /* VS code file */,
			);
			name = Desktop;
			path = Desktop;
			sourceTree = "<group>";
		};
		E4E29E5761E15A93CAF047FBFA7909E6 /* .. */ = {
			isa = PBXGroup;
			children = (
				995E85ADFD33BCF1AFB881B21A0A452E /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		E7B799A1AAB6899269D5B88735E6FF40 /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				7454FE3E037A917A16DA64C28E53AF77 /* Sources */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		E806A4F97D48F5FD3B4CA1BC617EA1F5 /* Pods-Runner */ = {
			isa = PBXGroup;
			children = (
				C24C1A035F22D24883C6F1876F274C6F /* Pods-Runner.modulemap */,
				ECDF6AA713870989438BA93B395DC557 /* Pods-Runner-acknowledgements.markdown */,
				1B136F41D5B5FAE96EA576E768A5771D /* Pods-Runner-acknowledgements.plist */,
				E794494AB90477BA94C681E994ADF341 /* Pods-Runner-dummy.m */,
				773BFCE41424EB8EBB72EF3F6A5FB719 /* Pods-Runner-frameworks.sh */,
				0AFB643DA4919253F749E2836A5AAFDC /* Pods-Runner-Info.plist */,
				499E3722E8DACDC2AAA6C15AD1796520 /* Pods-Runner-umbrella.h */,
				728A5ED1DD60BAC8C8F382A87B976F84 /* Pods-Runner.debug.xcconfig */,
				C50FD10CD19FF6999637BA5ECAF1681B /* Pods-Runner.profile.xcconfig */,
				B703785507B22BAF0B800737429577EC /* Pods-Runner.release.xcconfig */,
			);
			name = "Pods-Runner";
			path = "Target Support Files/Pods-Runner";
			sourceTree = "<group>";
		};
		E87689233F5DD20668961830E2507D47 /* Desktop */ = {
			isa = PBXGroup;
			children = (
				F76A57F042E4EA53EDE4118A21DA8DE5 /* VS code file */,
			);
			name = Desktop;
			path = Desktop;
			sourceTree = "<group>";
		};
		EA30E1898B8F82D74BC5FC63917025CD /* plugins */ = {
			isa = PBXGroup;
			children = (
				3BAB18968B0C72E479E8403BC1260B7B /* integration_test */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		EB08B4BF6981122717FB8D0F7514D623 /* Classes */ = {
			isa = PBXGroup;
			children = (
				F170D551E7111567220ED44EF78572C7 /* FlutterSecureStorage.swift */,
				E03F62DA0DAC1A423FAC06435FBBAF62 /* FlutterSecureStoragePlugin.h */,
				2449552EAE401BE4115E2B8C673DD3F2 /* FlutterSecureStoragePlugin.m */,
				D6E248DF5C2842C7779A52949C00917F /* SwiftFlutterSecureStoragePlugin.swift */,
			);
			name = Classes;
			path = Classes;
			sourceTree = "<group>";
		};
		F0A4E01A385C59DB542E92E35FBC7039 /* Resources */ = {
			isa = PBXGroup;
			children = (
				B6A123AA1A9854DFFC03245B97D03386 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			path = Resources;
			sourceTree = "<group>";
		};
		F0BCE5D2279C3A0D0F059BBF7EF920E3 /* .. */ = {
			isa = PBXGroup;
			children = (
				D6E9282D91833F2CD81CB5FF3A2AF42A /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		F31E127AE360C5A016375B1359233246 /* Sources */ = {
			isa = PBXGroup;
			children = (
				CADEACE6277C8451424869980BAC80A7 /* shared_preferences_foundation */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		F49966BFC72DC995B82FCEF77F6DA52B /* news_collector_app */ = {
			isa = PBXGroup;
			children = (
				7DBD56006889F5E1CDB6B05ACDCFAEA7 /* ios */,
			);
			name = news_collector_app;
			path = news_collector_app;
			sourceTree = "<group>";
		};
		F5E666977DBFED03BECBA39009CDCAA2 /* .. */ = {
			isa = PBXGroup;
			children = (
				830F5D8D612B7F6C4B4038568E5CB266 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		F76A57F042E4EA53EDE4118A21DA8DE5 /* VS code file */ = {
			isa = PBXGroup;
			children = (
				7B325632DCFEC9718752BB33E4EF2279 /* News_collector_UI */,
			);
			name = "VS code file";
			path = "VS code file";
			sourceTree = "<group>";
		};
		F88929676774334AD3ECF3B7551C69FF /* Pod */ = {
			isa = PBXGroup;
			children = (
				2566BDB90AD8A043A03EB3E43CA05B91 /* flutter_secure_storage.podspec */,
				5228F5FBCFB21D5149988CC4C7366D26 /* LICENSE */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		FA499B65D334C3D519E12323939DF37F /* plugins */ = {
			isa = PBXGroup;
			children = (
				1540493FB4D7BBB34AB78ADE3D7F9A5F /* flutter_secure_storage */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		FD3951177BC983626917ED903890475F /* .. */ = {
			isa = PBXGroup;
			children = (
				699079E047CDAF3C4A45E60F7EFF751D /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		FFFD9450D605A8DB42D836C6FAEA6B86 /* shared_preferences_foundation */ = {
			isa = PBXGroup;
			children = (
				C6F5C02C7B6AD0C44A1DAF36252613D2 /* .. */,
				390D22D6B689C97B09523EAD0ECE1F5F /* Pod */,
				DC9653E4AD344C1C5C39FF671E43B128 /* Support Files */,
			);
			name = shared_preferences_foundation;
			path = ../.symlinks/plugins/shared_preferences_foundation/darwin;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		3EDC6BE0B2553C1412F3A2BA4A3EFCE4 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				2631CEC725AA8C0549E5D2980171E0B0 /* path_provider_foundation-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		497D8623A20C822F1CC5424D3ABABFFA /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				7E757AF9A0CD9956DBF91D34E60B8788 /* Pods-Runner-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7D658CCB2512BD58C633CB8B2095F6C3 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				48ABC9CC212358D168731AEE56729598 /* shared_preferences_foundation-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D512C677EF50AA8088944739681C7285 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				7F35898B19881C1CE0B65100F140EABE /* flutter_secure_storage-umbrella.h in Headers */,
				DACB447D658FB7CEE4CDA65714EAB43F /* FlutterSecureStoragePlugin.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F4ADE2337A6718C5688929BDDE859230 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				569B4672244559E160E56125FCD6E7C6 /* FLTIntegrationTestRunner.h in Headers */,
				D3161FF34F8E2B45AD80A7B9450BE18B /* integration_test-umbrella.h in Headers */,
				406CAFAD6C9D83D830DB57BE4A39D10A /* IntegrationTestIosTest.h in Headers */,
				BBEB27BD1ED8774EC3EC3DEDE2F1FEFA /* IntegrationTestPlugin.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FC4DA9BD27A4914E75F803590E4C4B8B /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				EB2DC96CDCF638AB89007D2DB0F3119A /* Pods-RunnerTests-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		15490FE8E4E35B20C7777FA3316A00AA /* flutter_secure_storage */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C190184CBEB4522403C658DEFB7FE370 /* Build configuration list for PBXNativeTarget "flutter_secure_storage" */;
			buildPhases = (
				D512C677EF50AA8088944739681C7285 /* Headers */,
				995C510E6C163CCE6E828BE2BB8BAEB3 /* Sources */,
				55965BBBD5D76CA4DB7DFBB01AA39E58 /* Frameworks */,
				46D6D2785184EE87B0929A57D41C30D4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				72A154D3BA27E6A41FD98CCE73D870AD /* PBXTargetDependency */,
				399539D168EAEA178472681841FBA8BF /* PBXTargetDependency */,
			);
			name = flutter_secure_storage;
			productName = flutter_secure_storage;
			productReference = C1C1AD8C20613ACC67F00155D7BB54AA /* flutter_secure_storage */;
			productType = "com.apple.product-type.framework";
		};
		3232F0C0E7C65B232832393F9ADDD8C3 /* Pods-RunnerTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = D52099AA1537D0F3745166E1889F6CA3 /* Build configuration list for PBXNativeTarget "Pods-RunnerTests" */;
			buildPhases = (
				FC4DA9BD27A4914E75F803590E4C4B8B /* Headers */,
				001CCB0FEA80CD1C6BF534C6D9C6283A /* Sources */,
				4380924F566AA01EB048DC15F9BC6D33 /* Frameworks */,
				4A765108DEAFDEBF078F71CDDBE3414E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				3E50EE70D2BE4E8ADCC84537118765A9 /* PBXTargetDependency */,
			);
			name = "Pods-RunnerTests";
			productName = Pods_RunnerTests;
			productReference = 6C3345B1B3CAEDF5B03B1F731FDC492E /* Pods-RunnerTests */;
			productType = "com.apple.product-type.framework";
		};
		56F581DDCB0A032454E604885E17AE3C /* path_provider_foundation */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FC3EA6EE526A2F7266A44B11E3A1AD9A /* Build configuration list for PBXNativeTarget "path_provider_foundation" */;
			buildPhases = (
				3EDC6BE0B2553C1412F3A2BA4A3EFCE4 /* Headers */,
				05895E3E1AC56002880CEF9A9AC4086F /* Sources */,
				2C51CA6EC745ACB63E424757C710B36C /* Frameworks */,
				D412229AEE091A9D36A8553E8AA56B31 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				B565272DBFE6A32B98A8703E41A57356 /* PBXTargetDependency */,
				BFDEA6DFD6F63641D6751F1E869BC40A /* PBXTargetDependency */,
			);
			name = path_provider_foundation;
			productName = path_provider_foundation;
			productReference = AE157A33FEF959A214796BFF348717F6 /* path_provider_foundation */;
			productType = "com.apple.product-type.framework";
		};
		8B74B458B450D74B75744B87BD747314 /* Pods-Runner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 408115C22D5403E78169D2A9122AEE30 /* Build configuration list for PBXNativeTarget "Pods-Runner" */;
			buildPhases = (
				497D8623A20C822F1CC5424D3ABABFFA /* Headers */,
				20BE30C16E845B60F62BD41122A35E17 /* Sources */,
				847096248A5142E132437E75305D0198 /* Frameworks */,
				0946895ECCACC3C7079996384DD759E0 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				78685619032DE0791AADF0E3E9704326 /* PBXTargetDependency */,
				DE16B1672D4EB45D3E63048E2F8F0DB6 /* PBXTargetDependency */,
				FDDBAAA5E3C66437AC067C5CD869B875 /* PBXTargetDependency */,
				6FE55CE31651B8CAD05DCC4FEF21352E /* PBXTargetDependency */,
				B3C1C1737C787A8359A2D6D48AB12493 /* PBXTargetDependency */,
			);
			name = "Pods-Runner";
			productName = Pods_Runner;
			productReference = 669E8F25E1897672BDB80B7EB784DA24 /* Pods-Runner */;
			productType = "com.apple.product-type.framework";
		};
		AB5EE685B22D01885ADD930538E8DD3C /* shared_preferences_foundation */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 51E44378D0EE45FC26653A27A9669C35 /* Build configuration list for PBXNativeTarget "shared_preferences_foundation" */;
			buildPhases = (
				7D658CCB2512BD58C633CB8B2095F6C3 /* Headers */,
				413B07111961A3E5A9004A9C5FEDBC18 /* Sources */,
				C60368AE5AB595A6FB47AB86BB81F10B /* Frameworks */,
				B0C1F9DA0908C6404D0A7A4254A1F50F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				20FEAA10B2ACC27C20AB720EA668C8C9 /* PBXTargetDependency */,
				2D7CAB669D31CD199A516C99FCC2D913 /* PBXTargetDependency */,
			);
			name = shared_preferences_foundation;
			productName = shared_preferences_foundation;
			productReference = 93C6A45C6E4792269BE9BE0073839BF0 /* shared_preferences_foundation */;
			productType = "com.apple.product-type.framework";
		};
		ADE86197C9EC7D5D7AB212E24AE13395 /* integration_test */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C8A366432015022E0B3BEDAEC0168D7E /* Build configuration list for PBXNativeTarget "integration_test" */;
			buildPhases = (
				F4ADE2337A6718C5688929BDDE859230 /* Headers */,
				34385A7E8FBAE22793A2738188560FDF /* Sources */,
				D37529713F6219C15572CE565F23780E /* Frameworks */,
				65794A8FCF723FDAECE2B48E7537684A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				0BE7038233BE0AB690EF6395E4B47D38 /* PBXTargetDependency */,
			);
			name = integration_test;
			productName = integration_test;
			productReference = 5B707EA37CBC3DFDABC9D9DFAD54F4BD /* integration_test */;
			productType = "com.apple.product-type.framework";
		};
		B6AF8B7CEAF6321719ABBC7E770624DA /* shared_preferences_foundation-shared_preferences_foundation_privacy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9D1950AED929223FA7CEB2AC3C252626 /* Build configuration list for PBXNativeTarget "shared_preferences_foundation-shared_preferences_foundation_privacy" */;
			buildPhases = (
				D55106208131D77B8F670F02909FED07 /* Sources */,
				BFF5908BE98D5049B50C642FBF3FA67C /* Frameworks */,
				8C0751DB842AA21D55BD019DE9DC18BD /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "shared_preferences_foundation-shared_preferences_foundation_privacy";
			productName = shared_preferences_foundation_privacy;
			productReference = 0652FCE3FC19056983AABE058B3CC45B /* shared_preferences_foundation-shared_preferences_foundation_privacy */;
			productType = "com.apple.product-type.bundle";
		};
		BA5B0F40AD74C7512B1E4FD79F8F202F /* flutter_secure_storage-flutter_secure_storage */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E4DC6C66C0A6440FAA7AEF480ADF7423 /* Build configuration list for PBXNativeTarget "flutter_secure_storage-flutter_secure_storage" */;
			buildPhases = (
				EAAC5327E4663D6E55D08A950ED167E9 /* Sources */,
				B6E4FEEAF8CA9AEFFE9D7F258386DB89 /* Frameworks */,
				B3C31F4EDB00F3DBD5040C226B053981 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "flutter_secure_storage-flutter_secure_storage";
			productName = flutter_secure_storage;
			productReference = E3C330B5F4227ED6FDC9FC042B5115D5 /* flutter_secure_storage-flutter_secure_storage */;
			productType = "com.apple.product-type.bundle";
		};
		CACE6618E7996464E38687E13F67D945 /* path_provider_foundation-path_provider_foundation_privacy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 3C73E6CF2889D98D414BA510DA98E6D6 /* Build configuration list for PBXNativeTarget "path_provider_foundation-path_provider_foundation_privacy" */;
			buildPhases = (
				3330F5F4DDA53BDB7CD25FA784B3A8FF /* Sources */,
				C94696043CAD87B6B699BAB367B53A54 /* Frameworks */,
				457FA941F024601EAF8D17BE26A05F43 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "path_provider_foundation-path_provider_foundation_privacy";
			productName = path_provider_foundation_privacy;
			productReference = 3DBD4BADE27F8B91024E4B4B4DD75DB5 /* path_provider_foundation-path_provider_foundation_privacy */;
			productType = "com.apple.product-type.bundle";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BFDFE7DC352907FC980B868725387E98 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
			};
			buildConfigurationList = 4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = CF1408CF629C7361332E53B88F7BD30C;
			minimizedProjectReferenceProxies = 0;
			preferredProjectObjectVersion = 77;
			productRefGroup = 4AD764B1FD7A22E74A6D28B30224F69E /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */,
				15490FE8E4E35B20C7777FA3316A00AA /* flutter_secure_storage */,
				BA5B0F40AD74C7512B1E4FD79F8F202F /* flutter_secure_storage-flutter_secure_storage */,
				ADE86197C9EC7D5D7AB212E24AE13395 /* integration_test */,
				56F581DDCB0A032454E604885E17AE3C /* path_provider_foundation */,
				CACE6618E7996464E38687E13F67D945 /* path_provider_foundation-path_provider_foundation_privacy */,
				8B74B458B450D74B75744B87BD747314 /* Pods-Runner */,
				3232F0C0E7C65B232832393F9ADDD8C3 /* Pods-RunnerTests */,
				AB5EE685B22D01885ADD930538E8DD3C /* shared_preferences_foundation */,
				B6AF8B7CEAF6321719ABBC7E770624DA /* shared_preferences_foundation-shared_preferences_foundation_privacy */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		0946895ECCACC3C7079996384DD759E0 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		457FA941F024601EAF8D17BE26A05F43 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				2575DFFA510CC2B4234A4D50EA5F0D3F /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		46D6D2785184EE87B0929A57D41C30D4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				D8203AF03D60E3B7237E39C59F418E53 /* flutter_secure_storage-flutter_secure_storage in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4A765108DEAFDEBF078F71CDDBE3414E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		65794A8FCF723FDAECE2B48E7537684A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8C0751DB842AA21D55BD019DE9DC18BD /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				07E82B2FD0398AD3DD8C065F225532A4 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B0C1F9DA0908C6404D0A7A4254A1F50F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				B7C67D475D63B3D6501863172F8AD35F /* shared_preferences_foundation-shared_preferences_foundation_privacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B3C31F4EDB00F3DBD5040C226B053981 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				6AF898E6C70727BB0EB0128A6C51BB79 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D412229AEE091A9D36A8553E8AA56B31 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				14F8DD4CD78B04D048D64E6B0C8F4D0B /* path_provider_foundation-path_provider_foundation_privacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		001CCB0FEA80CD1C6BF534C6D9C6283A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				B375EA128E6579366091BAA390BBDD34 /* Pods-RunnerTests-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		05895E3E1AC56002880CEF9A9AC4086F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				92D06CE697A81B385ED6CD55784CB620 /* messages.g.swift in Sources */,
				E7798C39E72D9BEA2B31DD6A0E00CFEB /* path_provider_foundation-dummy.m in Sources */,
				8C47508E707EAC61A796BC803D08EB26 /* PathProviderPlugin.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		20BE30C16E845B60F62BD41122A35E17 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				E9DF8BE0593966A0EC9E1A0E6933F9DB /* Pods-Runner-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3330F5F4DDA53BDB7CD25FA784B3A8FF /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		34385A7E8FBAE22793A2738188560FDF /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				CCA077DFC736EDF948DED65DB4684718 /* FLTIntegrationTestRunner.m in Sources */,
				8A655B581F2FFA1A700C0AA32E29941E /* integration_test-dummy.m in Sources */,
				3BC0157C4228588BA0E7D4756DC8ED6B /* IntegrationTestIosTest.m in Sources */,
				A43F424C63EBD85B2861082DEA1BFB84 /* IntegrationTestPlugin.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		413B07111961A3E5A9004A9C5FEDBC18 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				E07748E423DD19D365E1E1D0CB75D3C6 /* messages.g.swift in Sources */,
				D2A52C6979E7137696C04242485D21EC /* shared_preferences_foundation-dummy.m in Sources */,
				43C8655BA99B44C6E8988F3AEA86BC3F /* SharedPreferencesPlugin.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		995C510E6C163CCE6E828BE2BB8BAEB3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				56FD98664C20CE8A5F7358B652BECD7E /* flutter_secure_storage-dummy.m in Sources */,
				EDD4406148BF8FB8427AE35A9F47C3A5 /* FlutterSecureStorage.swift in Sources */,
				8DA6CF83FBECBDD823728B1AD863A0C8 /* FlutterSecureStoragePlugin.m in Sources */,
				8FA3D968B971C1C6F83C5E2582A9033F /* SwiftFlutterSecureStoragePlugin.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D55106208131D77B8F670F02909FED07 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EAAC5327E4663D6E55D08A950ED167E9 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		0BE7038233BE0AB690EF6395E4B47D38 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = 42C1A90DDFBF3EAC73800E0D3B93B70E /* PBXContainerItemProxy */;
		};
		20FEAA10B2ACC27C20AB720EA668C8C9 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = 18B21F4F96C1C6E9CC2C9FE00FCD74BB /* PBXContainerItemProxy */;
		};
		2D7CAB669D31CD199A516C99FCC2D913 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "shared_preferences_foundation-shared_preferences_foundation_privacy";
			target = B6AF8B7CEAF6321719ABBC7E770624DA /* shared_preferences_foundation-shared_preferences_foundation_privacy */;
			targetProxy = 670B5623A895FC7CACE8A7A1A49C0143 /* PBXContainerItemProxy */;
		};
		399539D168EAEA178472681841FBA8BF /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "flutter_secure_storage-flutter_secure_storage";
			target = BA5B0F40AD74C7512B1E4FD79F8F202F /* flutter_secure_storage-flutter_secure_storage */;
			targetProxy = 6B524ACAA4B4A30A1C27A06275FC7443 /* PBXContainerItemProxy */;
		};
		3E50EE70D2BE4E8ADCC84537118765A9 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "Pods-Runner";
			target = 8B74B458B450D74B75744B87BD747314 /* Pods-Runner */;
			targetProxy = 46E6E8C84A79F1FDC236F3E473DD2F33 /* PBXContainerItemProxy */;
		};
		6FE55CE31651B8CAD05DCC4FEF21352E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = path_provider_foundation;
			target = 56F581DDCB0A032454E604885E17AE3C /* path_provider_foundation */;
			targetProxy = 63AA6E7D37193BDB050D1051167AA612 /* PBXContainerItemProxy */;
		};
		72A154D3BA27E6A41FD98CCE73D870AD /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = C574689BF6866CC554DDBEC547313975 /* PBXContainerItemProxy */;
		};
		78685619032DE0791AADF0E3E9704326 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = B2644DF8D110E38C3547754AD0E10180 /* PBXContainerItemProxy */;
		};
		B3C1C1737C787A8359A2D6D48AB12493 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = shared_preferences_foundation;
			target = AB5EE685B22D01885ADD930538E8DD3C /* shared_preferences_foundation */;
			targetProxy = 9C02CF0EBC978B2E1783AF68F90CCBFA /* PBXContainerItemProxy */;
		};
		B565272DBFE6A32B98A8703E41A57356 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Flutter;
			target = 1EFDDC32A34D56D411E640A81DCD9E73 /* Flutter */;
			targetProxy = FC3AFCB654FEB28BC13945E8F08F3818 /* PBXContainerItemProxy */;
		};
		BFDEA6DFD6F63641D6751F1E869BC40A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "path_provider_foundation-path_provider_foundation_privacy";
			target = CACE6618E7996464E38687E13F67D945 /* path_provider_foundation-path_provider_foundation_privacy */;
			targetProxy = 62F7F0624CD32DE6EA496D4E739E1F9F /* PBXContainerItemProxy */;
		};
		DE16B1672D4EB45D3E63048E2F8F0DB6 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = flutter_secure_storage;
			target = 15490FE8E4E35B20C7777FA3316A00AA /* flutter_secure_storage */;
			targetProxy = 2A3A3FD5575E6D671CB44AFD59FE704E /* PBXContainerItemProxy */;
		};
		FDDBAAA5E3C66437AC067C5CD869B875 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = integration_test;
			target = ADE86197C9EC7D5D7AB212E24AE13395 /* integration_test */;
			targetProxy = 4C9AC6E09E48A18FFBD034B6A397262E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		0316CA68318A621B67362408814959DE /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3C29A9A0523675E67F6E9B274BF36B06 /* path_provider_foundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/path_provider_foundation/path_provider_foundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = path_provider_foundation;
				PRODUCT_NAME = path_provider_foundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		124F00A55D7C890FB5AA9768B43A351E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 728A5ED1DD60BAC8C8F382A87B976F84 /* Pods-Runner.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-Runner/Pods-Runner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-Runner/Pods-Runner.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		162FDF3A324F3E4E46A4AE1A1AA123BF /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3C29A9A0523675E67F6E9B274BF36B06 /* path_provider_foundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/path_provider_foundation/path_provider_foundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = path_provider_foundation;
				PRODUCT_NAME = path_provider_foundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		1DF60998696FCBBD5D586032EB312CC6 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 34B0A9C091ED932F04FD1D3AF160CEB4 /* integration_test.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/integration_test/integration_test-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/integration_test/integration_test-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/integration_test/integration_test.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = integration_test;
				PRODUCT_NAME = integration_test;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		22CA6F6ED297F6C5C791279C7A8F0461 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E5AC58024FBF51814FC0BA9322824661 /* path_provider_foundation.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/path_provider_foundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = path_provider_foundation;
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = path_provider_foundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		2B9E26EAE2CD392AD762421F663075A1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
		2E4608C055BE4F4E675FECAEBC4CC2A4 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AC8FE152530FBDCE460186B326C1A8FB /* Pods-RunnerTests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		352238F70108F49434B51CFD9D646B3B /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F826B5A62132A1021AC1C0006243A58B /* shared_preferences_foundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = shared_preferences_foundation;
				PRODUCT_NAME = shared_preferences_foundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		380112534FF2B1ACDCE62D7FB9295180 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5EF8C76469D7ACFC619D6C69A7C2A8E7 /* shared_preferences_foundation.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/shared_preferences_foundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = shared_preferences_foundation;
				INFOPLIST_FILE = "Target Support Files/shared_preferences_foundation/ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = shared_preferences_foundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		386AC04547EE6FE3A4080B5C256377EE /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 34B0A9C091ED932F04FD1D3AF160CEB4 /* integration_test.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/integration_test/integration_test-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/integration_test/integration_test-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/integration_test/integration_test.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = integration_test;
				PRODUCT_NAME = integration_test;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		387AEA3F7EEDFE2FA70E42BB64B3B17F /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F826B5A62132A1021AC1C0006243A58B /* shared_preferences_foundation.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/shared_preferences_foundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = shared_preferences_foundation;
				INFOPLIST_FILE = "Target Support Files/shared_preferences_foundation/ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = shared_preferences_foundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		3969AB54D44FA1E48F12206DE1F32446 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A63E04ED2197F574A1C39A8AC54DC8B5 /* flutter_secure_storage.release.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/flutter_secure_storage/flutter_secure_storage-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/flutter_secure_storage/flutter_secure_storage-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/flutter_secure_storage/flutter_secure_storage.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = flutter_secure_storage;
				PRODUCT_NAME = flutter_secure_storage;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		46729596D3E861171179E26D7468B25C /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5EF8C76469D7ACFC619D6C69A7C2A8E7 /* shared_preferences_foundation.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = shared_preferences_foundation;
				PRODUCT_NAME = shared_preferences_foundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		47FF22822E1F91E1ED622A01F841DD45 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0A75BD300C59519EA3ABFB5D75CCDC0D /* flutter_secure_storage.debug.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/flutter_secure_storage/flutter_secure_storage-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/flutter_secure_storage/flutter_secure_storage-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/flutter_secure_storage/flutter_secure_storage.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = flutter_secure_storage;
				PRODUCT_NAME = flutter_secure_storage;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		4BDC17A7B185A436818197D3715CB00C /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0A75BD300C59519EA3ABFB5D75CCDC0D /* flutter_secure_storage.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/flutter_secure_storage";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = flutter_secure_storage;
				INFOPLIST_FILE = "Target Support Files/flutter_secure_storage/ResourceBundle-flutter_secure_storage-flutter_secure_storage-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_NAME = flutter_secure_storage;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		52F1DC8F0DE1A57C1142F1408292FEB9 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A63E04ED2197F574A1C39A8AC54DC8B5 /* flutter_secure_storage.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/flutter_secure_storage";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = flutter_secure_storage;
				INFOPLIST_FILE = "Target Support Files/flutter_secure_storage/ResourceBundle-flutter_secure_storage-flutter_secure_storage-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				PRODUCT_NAME = flutter_secure_storage;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		5AF1AC88E5F655C93D1E3B5012F6FDC8 /* Profile */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_PROFILE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Profile;
		};
		63FAF33E1C55B71A5F5A8B3CC8749F99 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		70F1E3D053D812E3D7E8E74934CC9053 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C832B181401ECD71E8170BF5694D9EF4 /* Flutter.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				ONLY_ACTIVE_ARCH = NO;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		768CE43E68DF2B5EF0C94D45F10B98DF /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F826B5A62132A1021AC1C0006243A58B /* shared_preferences_foundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = shared_preferences_foundation;
				PRODUCT_NAME = shared_preferences_foundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		76E053E47C7357CFF4EA96DF8337031F /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AA6B2500D7C0E65A71F2B236B48C62CC /* Flutter.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Profile;
		};
		788CDEFA5FA1C8060EA52CD880C04F04 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A63E04ED2197F574A1C39A8AC54DC8B5 /* flutter_secure_storage.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/flutter_secure_storage";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = flutter_secure_storage;
				INFOPLIST_FILE = "Target Support Files/flutter_secure_storage/ResourceBundle-flutter_secure_storage-flutter_secure_storage-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				PRODUCT_NAME = flutter_secure_storage;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Profile;
		};
		857BB3D5EB9F8B8635F38EE59B6FCA5D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 03E8AD2E8B75443E12D3D3BD12A9900E /* Pods-RunnerTests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		86C5BBE7CBBA66350B4E55FDF50CE26D /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E736E0C9C5B73D27B8865E363E2108AC /* Pods-RunnerTests.profile.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		A1202C59D5272B0DEE585943D867C807 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E5AC58024FBF51814FC0BA9322824661 /* path_provider_foundation.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/path_provider_foundation/path_provider_foundation-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MODULEMAP_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = path_provider_foundation;
				PRODUCT_NAME = path_provider_foundation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		A685FD63487E659ED1195CFB7C52B9EF /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B703785507B22BAF0B800737429577EC /* Pods-Runner.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-Runner/Pods-Runner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-Runner/Pods-Runner.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		B19F338034A241F06CE78582A345D8C2 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 410328C0734E96B2D80C03B3D59A6F59 /* integration_test.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/integration_test/integration_test-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/integration_test/integration_test-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/integration_test/integration_test.modulemap";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = integration_test;
				PRODUCT_NAME = integration_test;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		B4C900F3DB41CEEA566387FF7420F69B /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F826B5A62132A1021AC1C0006243A58B /* shared_preferences_foundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/shared_preferences_foundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = shared_preferences_foundation;
				INFOPLIST_FILE = "Target Support Files/shared_preferences_foundation/ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = shared_preferences_foundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Profile;
		};
		C96E12028EF0BEAB8FA109408527E7CB /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3C29A9A0523675E67F6E9B274BF36B06 /* path_provider_foundation.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/path_provider_foundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = path_provider_foundation;
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = path_provider_foundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		CC05485E7E515EB0D8E3769888E9B255 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A63E04ED2197F574A1C39A8AC54DC8B5 /* flutter_secure_storage.release.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/flutter_secure_storage/flutter_secure_storage-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/flutter_secure_storage/flutter_secure_storage-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/flutter_secure_storage/flutter_secure_storage.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				PRODUCT_MODULE_NAME = flutter_secure_storage;
				PRODUCT_NAME = flutter_secure_storage;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		E1B143BBE732E093A7D54231EDE31FE0 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C50FD10CD19FF6999637BA5ECAF1681B /* Pods-Runner.profile.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = "$(inherited) armv7";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "$(inherited) i386";
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\"",
					"$(inherited)",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"\"/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-Runner/Pods-Runner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-Runner/Pods-Runner.modulemap";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					Flutter,
				);
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				"VALID_ARCHS[sdk=iphonesimulator*]" = "$(ARCHS_STANDARD)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		E9C8BE3EB589AAB6BFE65F49E09BF1FC /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3C29A9A0523675E67F6E9B274BF36B06 /* path_provider_foundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_IDENTITY = "-";
				CODE_SIGNING_REQUIRED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/path_provider_foundation";
				EXPANDED_CODE_SIGN_IDENTITY = "-";
				IBSC_MODULE = path_provider_foundation;
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = path_provider_foundation_privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Profile;
		};
		FB4B781D3E4B2E85C6448C7B77E40A53 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AA6B2500D7C0E65A71F2B236B48C62CC /* Flutter.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		3C73E6CF2889D98D414BA510DA98E6D6 /* Build configuration list for PBXNativeTarget "path_provider_foundation-path_provider_foundation_privacy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				22CA6F6ED297F6C5C791279C7A8F0461 /* Debug */,
				E9C8BE3EB589AAB6BFE65F49E09BF1FC /* Profile */,
				C96E12028EF0BEAB8FA109408527E7CB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		408115C22D5403E78169D2A9122AEE30 /* Build configuration list for PBXNativeTarget "Pods-Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				124F00A55D7C890FB5AA9768B43A351E /* Debug */,
				E1B143BBE732E093A7D54231EDE31FE0 /* Profile */,
				A685FD63487E659ED1195CFB7C52B9EF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2B9E26EAE2CD392AD762421F663075A1 /* Debug */,
				5AF1AC88E5F655C93D1E3B5012F6FDC8 /* Profile */,
				63FAF33E1C55B71A5F5A8B3CC8749F99 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		51E44378D0EE45FC26653A27A9669C35 /* Build configuration list for PBXNativeTarget "shared_preferences_foundation" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				46729596D3E861171179E26D7468B25C /* Debug */,
				768CE43E68DF2B5EF0C94D45F10B98DF /* Profile */,
				352238F70108F49434B51CFD9D646B3B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		94078A06C17E946ADC1F2C06726219E5 /* Build configuration list for PBXAggregateTarget "Flutter" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				70F1E3D053D812E3D7E8E74934CC9053 /* Debug */,
				76E053E47C7357CFF4EA96DF8337031F /* Profile */,
				FB4B781D3E4B2E85C6448C7B77E40A53 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9D1950AED929223FA7CEB2AC3C252626 /* Build configuration list for PBXNativeTarget "shared_preferences_foundation-shared_preferences_foundation_privacy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				380112534FF2B1ACDCE62D7FB9295180 /* Debug */,
				B4C900F3DB41CEEA566387FF7420F69B /* Profile */,
				387AEA3F7EEDFE2FA70E42BB64B3B17F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C190184CBEB4522403C658DEFB7FE370 /* Build configuration list for PBXNativeTarget "flutter_secure_storage" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				47FF22822E1F91E1ED622A01F841DD45 /* Debug */,
				3969AB54D44FA1E48F12206DE1F32446 /* Profile */,
				CC05485E7E515EB0D8E3769888E9B255 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C8A366432015022E0B3BEDAEC0168D7E /* Build configuration list for PBXNativeTarget "integration_test" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B19F338034A241F06CE78582A345D8C2 /* Debug */,
				386AC04547EE6FE3A4080B5C256377EE /* Profile */,
				1DF60998696FCBBD5D586032EB312CC6 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		D52099AA1537D0F3745166E1889F6CA3 /* Build configuration list for PBXNativeTarget "Pods-RunnerTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				857BB3D5EB9F8B8635F38EE59B6FCA5D /* Debug */,
				86C5BBE7CBBA66350B4E55FDF50CE26D /* Profile */,
				2E4608C055BE4F4E675FECAEBC4CC2A4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E4DC6C66C0A6440FAA7AEF480ADF7423 /* Build configuration list for PBXNativeTarget "flutter_secure_storage-flutter_secure_storage" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4BDC17A7B185A436818197D3715CB00C /* Debug */,
				788CDEFA5FA1C8060EA52CD880C04F04 /* Profile */,
				52F1DC8F0DE1A57C1142F1408292FEB9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FC3EA6EE526A2F7266A44B11E3A1AD9A /* Build configuration list for PBXNativeTarget "path_provider_foundation" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A1202C59D5272B0DEE585943D867C807 /* Debug */,
				0316CA68318A621B67362408814959DE /* Profile */,
				162FDF3A324F3E4E46A4AE1A1AA123BF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BFDFE7DC352907FC980B868725387E98 /* Project object */;
}
