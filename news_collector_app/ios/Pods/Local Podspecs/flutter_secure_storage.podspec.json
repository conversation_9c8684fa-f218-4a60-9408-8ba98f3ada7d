{"name": "flutter_secure_storage", "version": "6.0.0", "summary": "A Flutter plugin to store data in secure storage.", "description": "A Flutter plugin to store data in secure storage.", "homepage": "http://example.com", "license": {"file": "../LICENSE"}, "authors": {"German Saprykin": "<EMAIL>"}, "source": {"path": "."}, "source_files": "Classes/**/*", "public_header_files": "Classes/**/*.h", "dependencies": {"Flutter": []}, "platforms": {"ios": "9.0"}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "i386"}, "swift_versions": "5.0", "resource_bundles": {"flutter_secure_storage": ["Resources/PrivacyInfo.xcprivacy"]}, "swift_version": "5.0"}