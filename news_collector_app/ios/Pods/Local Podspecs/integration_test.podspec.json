{"name": "integration_test", "version": "0.0.1", "summary": "Adapter for integration tests.", "description": "Runs tests that use the flutter_test API as integration tests.", "homepage": "https://github.com/flutter/flutter/tree/main/packages/integration_test", "license": {"type": "BSD", "text": "Use of this source code is governed by a BSD-style license that can be found in the LICENSE file.\n"}, "authors": {"Flutter Team": "<EMAIL>"}, "source": {"http": "https://github.com/flutter/flutter/tree/main/packages/integration_test"}, "source_files": "integration_test/Sources/integration_test/**/*.{h,m}", "public_header_files": "integration_test/Sources/integration_test/**/*.h", "dependencies": {"Flutter": []}, "ios": {"frameworks": "UIKit"}, "platforms": {"ios": "12.0"}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "i386"}}